# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=clinical_trials
DB_USER=user
DB_PASSWORD=password

# API Configuration
API_BASE_URL=https://clinicaltrials.gov/api/v2/studies
API_RATE_LIMIT_DELAY=0.5
API_MAX_RETRIES=3
API_TIMEOUT=30

# Data Collection Settings - PILOT TEST (very small dataset)
BATCH_SIZE=10
MAX_CONCURRENT_REQUESTS=1
DATA_START_DATE=2024-07-01
DATA_END_DATE=2024-07-03

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=pilot_test.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Processing Options
VALIDATE_DATA=true
EXTRACT_KEY_FIELDS=true
SKIP_EXISTING=false
ENABLE_RESUME=false