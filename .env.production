# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=clinical_trials
DB_USER=user
DB_PASSWORD=password

# API Configuration
API_BASE_URL=https://clinicaltrials.gov/api/v2/studies
API_RATE_LIMIT_DELAY=0.2
API_MAX_RETRIES=5
API_TIMEOUT=60

# Data Collection Settings - PRODUCTION (5 years of data)
BATCH_SIZE=1000
MAX_CONCURRENT_REQUESTS=3
DATA_START_DATE=2019-07-12
DATA_END_DATE=2025-07-12

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=production_collection.log
LOG_MAX_SIZE=500MB
LOG_BACKUP_COUNT=10

# Processing Options
VALIDATE_DATA=true
EXTRACT_KEY_FIELDS=true
SKIP_EXISTING=true
ENABLE_RESUME=true