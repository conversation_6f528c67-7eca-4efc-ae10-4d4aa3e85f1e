FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY fetch_trials_enhanced.py .
COPY .env .

# Create directory for logs
RUN mkdir -p /app/logs

# Run the application
CMD ["python", "fetch_trials_enhanced.py"]