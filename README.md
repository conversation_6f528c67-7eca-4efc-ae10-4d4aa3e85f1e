# Clinical Trials Data Collection System

A comprehensive system for collecting, storing, and analyzing clinical trials data from ClinicalTrials.gov API.

## 🎯 Project Overview

This system collects clinical trials data from the last 5 years to provide insights for developing clinical trial protocols. It includes:

- **Complete API data collection** from ClinicalTrials.gov
- **PostgreSQL database** with optimized schema and indexes
- **Robust error handling** with automatic retries and resumption
- **Data validation** and quality checks
- **Comprehensive logging** and monitoring
- **Flexible configuration** for different environments

## 📊 Current Status

✅ **COMPLETED TASKS:**
- [x] Database schema with comprehensive indexes
- [x] Docker-based PostgreSQL setup
- [x] Enhanced data collection script with all features
- [x] Error handling, retries, and resumption capability
- [x] Data validation and transformation
- [x] System testing and validation
- [x] Pilot test with sample data (9 studies collected successfully)
- [x] Validation report generation

📋 **READY FOR PRODUCTION:**
- System tested and validated
- Database optimized for large-scale data
- Error handling proven in pilot test
- Configuration files prepared

## 🏗️ System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ ClinicalTrials  │    │   Enhanced       │    │   PostgreSQL    │
│     .gov API    │───▶│  Fetch Script    │───▶│    Database     │
│                 │    │                  │    │                 │
│ • 500K+ studies │    │ • Error handling │    │ • Full JSON     │
│ • JSON format   │    │ • Validation     │    │ • Key fields    │
│ • Pagination    │    │ • Resumption     │    │ • Indexes       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Prerequisites
```bash
# Ensure Docker is running
docker --version
docker-compose --version
```

### 2. Start Database
```bash
# Start PostgreSQL container
docker-compose up -d

# Verify container is running
docker-compose ps
```

### 3. Initialize Database
```bash
# Create schema and tables
python3 test_db_simple.py
```

### 4. Run System Tests
```bash
# Validate entire system
python3 test_system.py
```

### 5. Production Data Collection
```bash
# Use production configuration
cp .env.production .env

# Start collection (will take several hours for 5 years of data)
python3 fetch_trials_enhanced.py

# Monitor progress
tail -f production_collection.log
```

## 📁 File Structure

```
trails/
├── 📄 README.md                    # This documentation
├── 🐳 docker-compose.yml          # PostgreSQL container setup
├── 🗄️ schema.sql                  # Database schema definition
├── 🔧 init_db.py                  # Database initialization script
├── 📋 requirements.txt             # Python dependencies
├── ⚙️ .env.example                # Configuration template
├── ⚙️ .env.production             # Production configuration
├── ⚙️ .env.pilot                  # Pilot test configuration
├── 🔄 fetch_trials_enhanced.py    # Main data collection script
├── 🧪 test_system.py              # System validation tests
├── 🧪 test_db_simple.py           # Simple database test
├── 🧪 run_pilot_test.py           # Pilot test runner
├── 📊 generate_report.py          # Data analysis and reporting
├── 📚 study-data-structure.md     # API data structure documentation
├── 📝 pilot_fetch.py              # Temporary pilot script
└── 📋 *.log                       # Log files
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default | Production |
|----------|-------------|---------|------------|
| `DB_HOST` | Database host | localhost | localhost |
| `DB_PORT` | Database port | 5432 | 5432 |
| `BATCH_SIZE` | Studies per API request | 1000 | 1000 |
| `API_RATE_LIMIT_DELAY` | Delay between requests (seconds) | 0.1 | 0.2 |
| `DATA_START_DATE` | Collection start date | 2019-07-12 | 2019-07-12 |
| `DATA_END_DATE` | Collection end date | 2025-07-12 | 2025-07-12 |
| `VALIDATE_DATA` | Enable data validation | true | true |
| `ENABLE_RESUME` | Allow resuming interrupted runs | true | true |

## 📊 Database Schema

### Main Tables

#### `studies` Table
- **Primary storage** for all clinical trial data
- **JSONB column** preserves complete API response
- **Extracted fields** for efficient querying
- **Indexes** on key fields for fast searches

#### `fetch_log` Table
- **Session tracking** for data collection runs
- **Progress monitoring** and statistics
- **Resume capability** for interrupted runs

### Key Indexes
- `idx_studies_nct_id` - Primary lookup
- `idx_studies_overall_status` - Status queries
- `idx_studies_dates_status` - Temporal analysis
- `idx_studies_data_gin` - JSONB full-text search

## 🧪 Testing & Validation

### System Tests
```bash
# Run all system tests
python3 test_system.py

# Expected output: 5/5 tests passed
```

### Pilot Test
```bash
# Test with small dataset
python3 run_pilot_test.py

# Expected: 9-10 studies collected
```

### Data Validation
```bash
# Generate comprehensive report
python3 generate_report.py > validation_report.txt
```

## 📈 Expected Data Volume

- **Time Range:** July 2019 - July 2025 (5 years)
- **Estimated Studies:** 500,000+ clinical trials
- **Database Size:** ~50-100 GB (estimated)
- **Collection Time:** 8-24 hours (depending on API limits)

## 🔍 Monitoring & Maintenance

### Real-time Monitoring
```bash
# Watch collection progress
tail -f production_collection.log

# Monitor database size
docker exec trails-db-1 psql -U user -d clinical_trials -c "SELECT pg_size_pretty(pg_database_size('clinical_trials'));"

# Check latest studies
docker exec trails-db-1 psql -U user -d clinical_trials -c "SELECT COUNT(*) FROM studies;"
```

### Resuming Interrupted Collections
The system automatically resumes from the last successful page if interrupted. No manual intervention required.

## 📊 Data Analysis

### Basic Queries
```sql
-- Total studies by status
SELECT 
    study_data->'protocolSection'->'statusModule'->>'overallStatus' as status,
    COUNT(*) 
FROM studies 
GROUP BY status 
ORDER BY count DESC;

-- Studies by sponsor type
SELECT 
    study_data->'protocolSection'->'sponsorCollaboratorsModule'->'leadSponsor'->>'class' as sponsor_class,
    COUNT(*) 
FROM studies 
GROUP BY sponsor_class;

-- Recent studies
SELECT 
    nct_id,
    study_data->'protocolSection'->'identificationModule'->>'briefTitle' as title,
    fetch_date
FROM studies 
ORDER BY fetch_date DESC 
LIMIT 10;
```

### Generate Reports
```bash
# Comprehensive analysis
python3 generate_report.py

# Export to file
python3 generate_report.py > analysis_report_$(date +%Y%m%d).txt
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check container status
   docker-compose ps
   
   # Restart if needed
   docker-compose down && docker-compose up -d
   ```

2. **API Rate Limiting**
   - Increase `API_RATE_LIMIT_DELAY` in configuration
   - The system automatically handles rate limits with exponential backoff

3. **Disk Space Issues**
   ```bash
   # Check available space
   df -h
   
   # Monitor database size
   docker exec trails-db-1 psql -U user -d clinical_trials -c "SELECT pg_size_pretty(pg_database_size('clinical_trials'));"
   ```

4. **Interrupted Collection**
   - Simply restart the collection script
   - Resume functionality is automatic when `ENABLE_RESUME=true`

### Performance Optimization

- **Batch Size:** Increase for faster collection, decrease if memory issues
- **Rate Limiting:** Balance between speed and API limits
- **Database:** Monitor index usage and query performance

## 🎯 Next Steps

1. **Start Production Collection**
   ```bash
   cp .env.production .env
   python3 fetch_trials_enhanced.py
   ```

2. **Monitor Progress**
   - Watch logs for progress updates
   - Check database growth
   - Validate data quality periodically

3. **Data Analysis**
   - Use SQL queries for specific insights
   - Generate regular reports
   - Develop analytics dashboards

4. **Maintenance**
   - Regular database backups
   - Log file rotation
   - Performance monitoring

## 📞 Support

For issues or questions:
1. Check troubleshooting section above
2. Review log files for error details
3. Validate system with test scripts
4. Ensure Docker containers are running properly

---

**Status:** ✅ System ready for production data collection  
**Last Updated:** July 12, 2025  
**Pilot Test:** ✅ Successful (9/10 studies collected)  
**Database:** ✅ Optimized and tested  
**API Integration:** ✅ Validated and robust