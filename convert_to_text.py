#!/usr/bin/env python3
"""
Convert clinical trial database entries to unstructured text documents.
Each trial is saved as a separate text file optimized for vector database ingestion.
"""

import os
import sys
import psycopg2
import psycopg2.extras
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('convert_to_text.log')
    ]
)
logger = logging.getLogger(__name__)

class ClinicalTrialTextConverter:
    """Convert clinical trial database entries to text documents."""
    
    def __init__(self, db_config: Dict[str, str], output_dir: str = "text_documents"):
        self.db_config = db_config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.processed_count = 0
        self.error_count = 0
        self.errors = []
        
    def connect_db(self):
        """Connect to the database."""
        return psycopg2.connect(**self.db_config)
    
    def format_date(self, date_obj) -> str:
        """Format date object to readable string."""
        if date_obj is None:
            return "Not specified"
        if isinstance(date_obj, datetime):
            return date_obj.strftime("%B %d, %Y")
        return str(date_obj)
    
    def format_list(self, items: List[str], prefix: str = "- ") -> str:
        """Format a list of items with prefix."""
        if not items:
            return "None specified"
        return "\n".join(f"{prefix}{item}" for item in items)
    
    def extract_text_from_study(self, row: Dict[str, Any]) -> str:
        """Convert a study row to formatted text document."""
        # Extract basic fields
        nct_id = row['nct_id']
        brief_title = row['brief_title'] or "Not specified"
        official_title = row['official_title'] or brief_title
        
        # Extract JSON data
        study_data = row['study_data']
        protocol = study_data.get('protocolSection', {})
        
        # Build the text document
        lines = [
            f"Clinical Trial: {nct_id}",
            f"Title: {brief_title}",
            f"Official Title: {official_title}",
            "",
            "="*80,
            "",
            "OVERVIEW",
            "-"*40
        ]
        
        # Add brief summary
        desc_module = protocol.get('descriptionModule', {})
        brief_summary = desc_module.get('briefSummary', 'No summary available.')
        lines.extend([brief_summary, ""])
        
        # Study details
        lines.extend([
            "STUDY DETAILS",
            "-"*40,
            f"- Status: {row['overall_status'] or 'Not specified'}",
            f"- Study Type: {row['study_type'] or 'Not specified'}",
            f"- Phase: {row['phase'] or 'Not applicable'}",
            f"- Enrollment: {row['enrollment_count'] or 'Not specified'} {row['enrollment_type'] or 'participants'}",
            f"- Start Date: {self.format_date(row['start_date'])} ({row['start_date_type'] or 'Actual'})",
            f"- Primary Completion Date: {self.format_date(row['primary_completion_date'])}",
            f"- Study Completion Date: {self.format_date(row['completion_date'])}",
            ""
        ])
        
        # Detailed description
        if desc_module.get('detailedDescription'):
            lines.extend([
                "DESCRIPTION",
                "-"*40,
                desc_module['detailedDescription'],
                ""
            ])
        
        # Conditions and keywords
        cond_module = protocol.get('conditionsModule', {})
        conditions = cond_module.get('conditions', [])
        keywords = cond_module.get('keywords', [])
        
        lines.extend([
            "CONDITIONS AND KEYWORDS",
            "-"*40,
            f"This study focuses on: {', '.join(conditions) if conditions else 'Not specified'}",
            f"Keywords: {', '.join(keywords[:10]) if keywords else 'None specified'}",
            ""
        ])
        
        # Interventions
        arms_module = protocol.get('armsInterventionsModule', {})
        interventions = arms_module.get('interventions', [])
        
        if interventions:
            lines.extend([
                "INTERVENTIONS",
                "-"*40
            ])
            for intervention in interventions:
                int_type = intervention.get('type', 'Unknown')
                int_name = intervention.get('name', 'Unknown')
                int_desc = intervention.get('description', '')
                lines.append(f"- {int_type}: {int_name}")
                if int_desc:
                    lines.append(f"  Description: {int_desc}")
            lines.append("")
        
        # Eligibility
        eligibility = protocol.get('eligibilityModule', {})
        lines.extend([
            "ELIGIBILITY",
            "-"*40,
            f"Age Range: {row['minimum_age'] or 'Not specified'} to {row['maximum_age'] or 'Not specified'}",
            f"Sex: {row['sex'] or 'All'}",
            f"Accepts Healthy Volunteers: {'Yes' if row['accepts_healthy_volunteers'] else 'No'}",
            ""
        ])
        
        if eligibility.get('eligibilityCriteria'):
            lines.extend([
                "Eligibility Criteria:",
                eligibility['eligibilityCriteria'],
                ""
            ])
        
        # Outcomes
        outcomes_module = protocol.get('outcomesModule', {})
        primary_outcomes = outcomes_module.get('primaryOutcomes', [])
        secondary_outcomes = outcomes_module.get('secondaryOutcomes', [])
        
        if primary_outcomes or secondary_outcomes:
            lines.extend([
                "OUTCOMES",
                "-"*40
            ])
            
            if primary_outcomes:
                lines.append("Primary Outcomes:")
                for outcome in primary_outcomes:
                    measure = outcome.get('measure', 'Not specified')
                    desc = outcome.get('description', '')
                    timeframe = outcome.get('timeFrame', 'Not specified')
                    lines.append(f"- {measure}")
                    if desc:
                        lines.append(f"  Description: {desc}")
                    lines.append(f"  Time Frame: {timeframe}")
                lines.append("")
            
            if secondary_outcomes:
                lines.append("Secondary Outcomes:")
                for outcome in secondary_outcomes[:5]:  # Limit to first 5
                    measure = outcome.get('measure', 'Not specified')
                    timeframe = outcome.get('timeFrame', 'Not specified')
                    lines.append(f"- {measure} (Time Frame: {timeframe})")
                if len(secondary_outcomes) > 5:
                    lines.append(f"  ... and {len(secondary_outcomes) - 5} more secondary outcomes")
                lines.append("")
        
        # Study design
        design_module = protocol.get('designModule', {})
        if design_module:
            lines.extend([
                "STUDY DESIGN",
                "-"*40
            ])
            
            study_type = design_module.get('studyType', 'Not specified')
            lines.append(f"- Study Type: {study_type}")
            
            if design_module.get('phases'):
                phases = design_module['phases']
                lines.append(f"- Phases: {', '.join(phases)}")
            
            design_info = design_module.get('designInfo', {})
            if design_info:
                allocation = design_info.get('allocation', 'Not specified')
                masking_info = design_info.get('maskingInfo', {})
                masking = masking_info.get('masking', 'None')
                primary_purpose = design_info.get('primaryPurpose', 'Not specified')
                
                lines.extend([
                    f"- Allocation: {allocation}",
                    f"- Masking: {masking}",
                    f"- Primary Purpose: {primary_purpose}"
                ])
            lines.append("")
        
        # Arms and groups
        arms = arms_module.get('armGroups', [])
        if arms:
            lines.extend([
                "ARMS AND GROUPS",
                "-"*40
            ])
            for arm in arms[:5]:  # Limit to first 5
                label = arm.get('label', 'Unknown')
                arm_type = arm.get('type', 'Unknown')
                desc = arm.get('description', 'No description')
                lines.append(f"- {label} ({arm_type})")
                lines.append(f"  {desc}")
            if len(arms) > 5:
                lines.append(f"... and {len(arms) - 5} more study arms")
            lines.append("")
        
        # Locations
        locations_module = protocol.get('contactsLocationsModule', {})
        locations = locations_module.get('locations', [])
        
        if locations:
            lines.extend([
                "LOCATIONS",
                "-"*40
            ])
            
            # Group by country
            countries = {}
            for loc in locations:
                country = loc.get('country', 'Unknown')
                if country not in countries:
                    countries[country] = []
                city = loc.get('city', 'Unknown')
                state = loc.get('state', '')
                location_str = f"{city}, {state}" if state else city
                countries[country].append(location_str)
            
            for country, cities in sorted(countries.items()):
                lines.append(f"- {country}: {', '.join(cities[:5])}")
                if len(cities) > 5:
                    lines.append(f"  ... and {len(cities) - 5} more locations in {country}")
            lines.append("")
        
        # Sponsor and collaborators
        sponsor_module = protocol.get('sponsorCollaboratorsModule', {})
        lead_sponsor = sponsor_module.get('leadSponsor', {})
        collaborators = sponsor_module.get('collaborators', [])
        
        lines.extend([
            "SPONSOR AND COLLABORATORS",
            "-"*40,
            f"Lead Sponsor: {lead_sponsor.get('name', row['sponsor_name'] or 'Not specified')}",
            f"Sponsor Type: {lead_sponsor.get('class', 'Not specified')}"
        ])
        
        if collaborators:
            lines.append("Collaborators:")
            for collab in collaborators[:5]:
                lines.append(f"- {collab.get('name', 'Unknown')} ({collab.get('class', 'Unknown')})")
            if len(collaborators) > 5:
                lines.append(f"... and {len(collaborators) - 5} more collaborators")
        lines.append("")
        
        # Additional information
        lines.extend([
            "ADDITIONAL INFORMATION",
            "-"*40,
            f"- First Posted: {self.format_date(row['study_first_posted_date'])}",
            f"- Last Updated: {self.format_date(row['last_update_posted_date'])}",
            f"- Results Available: {'Yes' if row['has_results'] else 'No'}",
            f"- Has US Facility: {'Yes' if row['has_us_facility'] else 'No'}",
            f"- Has International Facility: {'Yes' if row['has_international_facility'] else 'No'}",
            f"- ClinicalTrials.gov ID: {nct_id}",
            f"- URL: https://clinicaltrials.gov/study/{nct_id}"
        ])
        
        return "\n".join(lines)
    
    def save_text_file(self, nct_id: str, content: str) -> Path:
        """Save text content to file."""
        # Create subdirectory based on NCT number for organization
        # e.g., NCT12345678 -> NCT123/NCT12345678.txt
        subdir = self.output_dir / nct_id[:6]
        subdir.mkdir(exist_ok=True)
        
        file_path = subdir / f"{nct_id}.txt"
        file_path.write_text(content, encoding='utf-8')
        return file_path
    
    def process_batch(self, batch_size: int = 1000, offset: int = 0, limit: Optional[int] = None):
        """Process a batch of studies."""
        conn = self.connect_db()
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        try:
            # Get total count
            if limit is None:
                cur.execute("SELECT COUNT(*) FROM studies")
                total_count = cur.fetchone()[0]
            else:
                total_count = limit
            
            # Process in batches
            processed = 0
            logger.info(f"Starting to process {total_count:,} studies...")
            
            while processed < total_count:
                # Calculate batch size for this iteration
                current_batch_size = min(batch_size, total_count - processed)
                
                # Fetch batch
                query = """
                    SELECT * FROM studies 
                    ORDER BY nct_id 
                    LIMIT %s OFFSET %s
                """
                cur.execute(query, (current_batch_size, offset + processed))
                rows = cur.fetchall()
                
                if not rows:
                    break
                
                # Process each row
                for row in rows:
                    try:
                        nct_id = row['nct_id']
                        text_content = self.extract_text_from_study(row)
                        file_path = self.save_text_file(nct_id, text_content)
                        self.processed_count += 1
                        
                        # Log progress periodically
                        if self.processed_count % 100 == 0:
                            progress_pct = ((offset + self.processed_count) / total_count) * 100
                            logger.info(f"Progress: {self.processed_count:,} / {total_count:,} ({progress_pct:.1f}%)")
                    except Exception as e:
                        self.error_count += 1
                        self.errors.append({
                            'nct_id': row.get('nct_id', 'Unknown'),
                            'error': str(e)
                        })
                        logger.error(f"Error processing {row.get('nct_id', 'Unknown')}: {e}")
                
                processed += len(rows)
                
                # Log batch completion
                if processed % 1000 == 0:
                    logger.info(f"Completed batch: {processed:,} / {total_count:,} studies processed")
        
        finally:
            cur.close()
            conn.close()
    
    def generate_report(self):
        """Generate processing report."""
        report_path = self.output_dir / "processing_report.txt"
        with open(report_path, 'w') as f:
            f.write(f"Clinical Trial Text Conversion Report\n")
            f.write(f"Generated: {datetime.now()}\n")
            f.write(f"{'='*50}\n\n")
            f.write(f"Total Processed: {self.processed_count:,}\n")
            f.write(f"Errors: {self.error_count:,}\n")
            f.write(f"Success Rate: {(self.processed_count / (self.processed_count + self.error_count) * 100):.2f}%\n")
            
            if self.errors:
                f.write(f"\nError Details:\n")
                for error in self.errors[:100]:  # First 100 errors
                    f.write(f"- {error['nct_id']}: {error['error']}\n")
                if len(self.errors) > 100:
                    f.write(f"... and {len(self.errors) - 100} more errors\n")
        
        logger.info(f"Report saved to: {report_path}")

def get_db_config():
    """Get database configuration."""
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', '5432'),
        'database': os.getenv('DB_NAME', 'clinical_trials'),
        'user': os.getenv('DB_USER', 'user'),
        'password': os.getenv('DB_PASSWORD', 'password')
    }

def main():
    parser = argparse.ArgumentParser(description='Convert clinical trials to text documents')
    parser.add_argument('--batch-size', type=int, default=1000, help='Batch size for processing')
    parser.add_argument('--offset', type=int, default=0, help='Starting offset')
    parser.add_argument('--limit', type=int, help='Limit number of studies to process')
    parser.add_argument('--output-dir', default='text_documents', help='Output directory')
    
    args = parser.parse_args()
    
    # Initialize converter
    db_config = get_db_config()
    converter = ClinicalTrialTextConverter(db_config, args.output_dir)
    
    logger.info(f"Starting conversion process...")
    logger.info(f"Output directory: {converter.output_dir}")
    
    # Process studies
    converter.process_batch(
        batch_size=args.batch_size,
        offset=args.offset,
        limit=args.limit
    )
    
    # Generate report
    converter.generate_report()
    
    logger.info(f"Conversion complete! Processed {converter.processed_count:,} studies")
    if converter.error_count > 0:
        logger.warning(f"Encountered {converter.error_count} errors during processing")

if __name__ == "__main__":
    main()