import requests
import json
import psycopg2
from datetime import datetime, timedelta

# Calculate date 5 years ago
five_years_ago = (datetime.now() - timedelta(days=5*365)).strftime('%Y-%m-%d')
current_date = datetime.now().strftime('%Y-%m-%d')

# Connect to PostgreSQL
conn = psycopg2.connect(
    dbname='clinical_trials',
    user='user',
    password='password',
    host='localhost',
    port='5432'
)
cur = conn.cursor()

def fetch_and_insert_studies():
    base_url = "https://clinicaltrials.gov/api/v2/studies"
    params = {
        'format': 'json',
        'pageSize': 1000,
        'filter.dates.startFrom': five_years_ago,
        'filter.dates.startTo': current_date
    }
    next_page_token = None

    while True:
        if next_page_token:
            params['pageToken'] = next_page_token

        response = requests.get(base_url, params=params)
        if response.status_code != 200:
            print(f"Error: {response.status_code}")
            break

        data = response.json()
        studies = data.get('studies', [])

        for study in studies:
            nct_id = study['protocolSection']['identificationModule']['nctId']
            study_json = json.dumps(study)
            cur.execute(
                """
                INSERT INTO studies (nct_id, study_data)
                VALUES (%s, %s)
                ON CONFLICT (nct_id) DO UPDATE SET study_data = EXCLUDED.study_data, fetch_date = CURRENT_TIMESTAMP
                """,
                (nct_id, study_json)
            )

        conn.commit()
        print(f"Inserted {len(studies)} studies.")

        next_page_token = data.get('nextPageToken')
        if not next_page_token:
            break

fetch_and_insert_studies()

cur.close()
conn.close()