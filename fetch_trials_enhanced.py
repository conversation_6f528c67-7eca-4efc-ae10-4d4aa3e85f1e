#!/usr/bin/env python3
"""
Enhanced Clinical Trials Data Fetcher
Comprehensive script to fetch clinical trials data from ClinicalTrials.gov API
with robust error handling, logging, data validation, and resumption capability.
"""

import os
import sys
import json
import time
import logging
import psycopg2
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import signal
from pathlib import Path

# Try to import optional dependencies
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False
    print("Warning: python-dotenv not available. Using environment variables directly.")

class ClinicalTrialsFetcher:
    """Enhanced clinical trials data fetcher with comprehensive features."""
    
    def __init__(self):
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.db_conn = None
        self.db_cursor = None
        self.fetch_log_id = None
        self.shutdown_requested = False
        self.stats = {
            'fetched': 0,
            'updated': 0,
            'skipped': 0,
            'errors': 0,
            'start_time': None
        }
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _load_config(self) -> Dict:
        """Load configuration from environment variables."""
        return {
            # Database settings
            'db_host': os.getenv('DB_HOST', 'localhost'),
            'db_port': int(os.getenv('DB_PORT', '5432')),
            'db_name': os.getenv('DB_NAME', 'clinical_trials'),
            'db_user': os.getenv('DB_USER', 'user'),
            'db_password': os.getenv('DB_PASSWORD', 'password'),
            
            # API settings
            'api_base_url': os.getenv('API_BASE_URL', 'https://clinicaltrials.gov/api/v2/studies'),
            'api_rate_limit_delay': float(os.getenv('API_RATE_LIMIT_DELAY', '0.1')),
            'api_max_retries': int(os.getenv('API_MAX_RETRIES', '3')),
            'api_timeout': int(os.getenv('API_TIMEOUT', '30')),
            
            # Data collection settings
            'batch_size': int(os.getenv('BATCH_SIZE', '1000')),
            'data_start_date': os.getenv('DATA_START_DATE', '2019-07-12'),
            'data_end_date': os.getenv('DATA_END_DATE', '2025-07-12'),
            
            # Processing options
            'validate_data': os.getenv('VALIDATE_DATA', 'true').lower() == 'true',
            'extract_key_fields': os.getenv('EXTRACT_KEY_FIELDS', 'true').lower() == 'true',
            'skip_existing': os.getenv('SKIP_EXISTING', 'true').lower() == 'true',
            'enable_resume': os.getenv('ENABLE_RESUME', 'true').lower() == 'true',
            
            # Logging
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            'log_file': os.getenv('LOG_FILE', 'clinical_trials.log'),
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger('clinical_trials_fetcher')
        logger.setLevel(getattr(logging, self.config['log_level']))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_format)
        logger.addHandler(console_handler)
        
        # File handler
        file_handler = logging.FileHandler(self.config['log_file'])
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_format)
        logger.addHandler(file_handler)
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}. Initiating graceful shutdown...")
        self.shutdown_requested = True
    
    def connect_database(self) -> bool:
        """Establish database connection."""
        try:
            self.logger.info("Connecting to database...")
            self.db_conn = psycopg2.connect(
                host=self.config['db_host'],
                port=self.config['db_port'],
                database=self.config['db_name'],
                user=self.config['db_user'],
                password=self.config['db_password']
            )
            self.db_cursor = self.db_conn.cursor()
            self.logger.info("✅ Database connection established")
            return True
        except Exception as e:
            self.logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def start_fetch_session(self) -> bool:
        """Start a new fetch session and log it."""
        try:
            filter_params = {
                'format': 'json',
                'pageSize': self.config['batch_size']
            }
            
            if self.config['data_start_date']:
                filter_params['filter.dates.startFrom'] = self.config['data_start_date']
            if self.config['data_end_date']:
                filter_params['filter.dates.startTo'] = self.config['data_end_date']
            
            self.db_cursor.execute("""
                INSERT INTO fetch_log (filter_params, notes)
                VALUES (%s, %s)
                RETURNING id
            """, (json.dumps(filter_params), f"Enhanced fetch session started"))
            
            self.fetch_log_id = self.db_cursor.fetchone()[0]
            self.db_conn.commit()
            self.logger.info(f"📝 Started fetch session ID: {self.fetch_log_id}")
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to start fetch session: {e}")
            return False
    
    def find_resume_point(self) -> Optional[str]:
        """Find the last page token from an interrupted session."""
        if not self.config['enable_resume']:
            return None
        
        try:
            self.db_cursor.execute("""
                SELECT last_page_token, studies_fetched
                FROM fetch_log
                WHERE status = 'interrupted'
                ORDER BY fetch_start DESC
                LIMIT 1
            """)
            result = self.db_cursor.fetchone()
            
            if result and result[0]:
                token, count = result
                self.logger.info(f"🔄 Resuming from page token: {token} (after {count} studies)")
                return token
            return None
        except Exception as e:
            self.logger.error(f"❌ Error finding resume point: {e}")
            return None
    
    def extract_key_fields(self, study: Dict) -> Dict:
        """Extract key fields from study data for efficient querying."""
        if not self.config['extract_key_fields']:
            return {}
        
        try:
            protocol = study.get('protocolSection', {})
            identification = protocol.get('identificationModule', {})
            status = protocol.get('statusModule', {})
            design = protocol.get('designModule', {})
            eligibility = protocol.get('eligibilityModule', {})
            sponsor = protocol.get('sponsorCollaboratorsModule', {})
            contacts = protocol.get('contactsLocationsModule', {})
            arms = protocol.get('armsInterventionsModule', {})
            
            # Extract dates safely
            def safe_date(date_str):
                if not date_str:
                    return None
                try:
                    return datetime.strptime(date_str, '%Y-%m-%d').date()
                except:
                    try:
                        return datetime.strptime(date_str, '%Y-%m').date()
                    except:
                        return None
            
            # Extract key fields
            fields = {
                'brief_title': identification.get('briefTitle'),
                'official_title': identification.get('officialTitle'),
                'overall_status': status.get('overallStatus'),
                'study_type': design.get('studyType'),
                'phase': ','.join(design.get('phases', [])) if design.get('phases') else None,
                
                # Dates
                'start_date': safe_date(status.get('startDateStruct', {}).get('date')),
                'start_date_type': status.get('startDateStruct', {}).get('type'),
                'primary_completion_date': safe_date(status.get('primaryCompletionDateStruct', {}).get('date')),
                'primary_completion_date_type': status.get('primaryCompletionDateStruct', {}).get('type'),
                'completion_date': safe_date(status.get('completionDateStruct', {}).get('date')),
                'completion_date_type': status.get('completionDateStruct', {}).get('type'),
                'study_first_submit_date': safe_date(status.get('studyFirstSubmitDate')),
                'study_first_posted_date': safe_date(status.get('studyFirstPostDate')),
                'last_update_submit_date': safe_date(status.get('lastUpdateSubmitDate')),
                'last_update_posted_date': safe_date(status.get('lastUpdatePostDate')),
                
                # Organization
                'sponsor_name': sponsor.get('leadSponsor', {}).get('name'),
                'responsible_party_type': sponsor.get('responsibleParty', {}).get('type'),
                
                # Study characteristics
                'enrollment_count': design.get('enrollmentInfo', {}).get('count'),
                'enrollment_type': design.get('enrollmentInfo', {}).get('type'),
                'number_of_arms': len(arms.get('armGroups', [])) if arms.get('armGroups') else None,
                
                # Eligibility
                'minimum_age': eligibility.get('minimumAge'),
                'maximum_age': eligibility.get('maximumAge'),
                'sex': eligibility.get('sex'),
                'accepts_healthy_volunteers': eligibility.get('healthyVolunteers') == 'Accepts Healthy Volunteers',
                
                # Geographic info
                'has_us_facility': self._has_us_facility(contacts),
                'has_international_facility': self._has_international_facility(contacts),
                
                # Results
                'has_results': 'resultsSection' in study,
                'has_expanded_access': status.get('expandedAccessInfo', {}).get('hasExpandedAccess') == 'true'
            }
            
            return fields
        except Exception as e:
            self.logger.debug(f"Error extracting key fields: {e}")
            return {}
    
    def _has_us_facility(self, contacts: Dict) -> Optional[bool]:
        """Check if study has US facilities."""
        locations = contacts.get('locations', [])
        if not locations:
            return None
        return any(loc.get('country') == 'United States' for loc in locations)
    
    def _has_international_facility(self, contacts: Dict) -> Optional[bool]:
        """Check if study has international facilities."""
        locations = contacts.get('locations', [])
        if not locations:
            return None
        return any(loc.get('country') != 'United States' for loc in locations)
    
    def validate_study_data(self, study: Dict) -> bool:
        """Validate study data structure."""
        if not self.config['validate_data']:
            return True
        
        try:
            # Check required top-level structure
            if 'protocolSection' not in study:
                self.logger.debug("Missing protocolSection")
                return False
            
            protocol = study['protocolSection']
            
            # Check required modules
            required_modules = ['identificationModule', 'statusModule']
            for module in required_modules:
                if module not in protocol:
                    self.logger.debug(f"Missing required module: {module}")
                    return False
            
            # Check NCT ID format
            nct_id = protocol.get('identificationModule', {}).get('nctId')
            if not nct_id or not nct_id.startswith('NCT') or len(nct_id) != 11:
                self.logger.debug(f"Invalid NCT ID: {nct_id}")
                return False
            
            return True
        except Exception as e:
            self.logger.debug(f"Validation error: {e}")
            return False
    
    def insert_or_update_study(self, study: Dict) -> bool:
        """Insert or update study in database."""
        try:
            nct_id = study['protocolSection']['identificationModule']['nctId']
            
            # Check if study exists if skip_existing is enabled
            if self.config['skip_existing']:
                self.db_cursor.execute("SELECT 1 FROM studies WHERE nct_id = %s", (nct_id,))
                if self.db_cursor.fetchone():
                    self.stats['skipped'] += 1
                    return True
            
            # Extract key fields
            key_fields = self.extract_key_fields(study)
            study_json = json.dumps(study)
            
            # Prepare insert/update query
            if key_fields:
                # Build dynamic query with key fields
                field_names = list(key_fields.keys())
                field_placeholders = ', '.join(['%s'] * len(field_names))
                field_assignments = ', '.join([f"{name} = EXCLUDED.{name}" for name in field_names])
                
                query = f"""
                    INSERT INTO studies (nct_id, study_data, {', '.join(field_names)})
                    VALUES (%s, %s, {field_placeholders})
                    ON CONFLICT (nct_id) DO UPDATE SET
                        study_data = EXCLUDED.study_data,
                        last_updated = CURRENT_TIMESTAMP,
                        {field_assignments}
                """
                
                values = [nct_id, study_json] + list(key_fields.values())
            else:
                # Simple insert with just JSON data
                query = """
                    INSERT INTO studies (nct_id, study_data)
                    VALUES (%s, %s)
                    ON CONFLICT (nct_id) DO UPDATE SET
                        study_data = EXCLUDED.study_data,
                        last_updated = CURRENT_TIMESTAMP
                """
                values = [nct_id, study_json]
            
            self.db_cursor.execute(query, values)
            
            if self.db_cursor.rowcount > 0:
                self.stats['fetched'] += 1
            else:
                self.stats['updated'] += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error inserting study {nct_id}: {e}")
            self.stats['errors'] += 1
            return False
    
    def fetch_with_retry(self, url: str, params: Dict) -> Optional[Dict]:
        """Fetch data from API with retry logic."""
        for attempt in range(self.config['api_max_retries']):
            try:
                response = requests.get(
                    url, 
                    params=params, 
                    timeout=self.config['api_timeout']
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limited
                    wait_time = min(2 ** attempt, 60)  # Exponential backoff, max 60s
                    self.logger.warning(f"Rate limited, waiting {wait_time}s...")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"API error {response.status_code}: {response.text}")
                    return None
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Request timeout (attempt {attempt + 1}/{self.config['api_max_retries']})")
            except Exception as e:
                self.logger.error(f"Request error: {e}")
                if attempt == self.config['api_max_retries'] - 1:
                    return None
            
            if attempt < self.config['api_max_retries'] - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
        
        return None
    
    def update_fetch_log(self, status: str, last_page_token: Optional[str] = None):
        """Update fetch log with current progress."""
        try:
            if self.fetch_log_id:
                self.db_cursor.execute("""
                    UPDATE fetch_log SET
                        fetch_end = CURRENT_TIMESTAMP,
                        status = %s,
                        studies_fetched = %s,
                        studies_updated = %s,
                        studies_skipped = %s,
                        last_page_token = %s,
                        total_studies = %s
                    WHERE id = %s
                """, (
                    status,
                    self.stats['fetched'],
                    self.stats['updated'],
                    self.stats['skipped'],
                    last_page_token,
                    self.stats['fetched'] + self.stats['updated'] + self.stats['skipped'],
                    self.fetch_log_id
                ))
                self.db_conn.commit()
        except Exception as e:
            self.logger.error(f"❌ Error updating fetch log: {e}")
    
    def fetch_all_studies(self):
        """Main function to fetch all studies."""
        self.logger.info("🚀 Starting clinical trials data collection...")
        self.stats['start_time'] = time.time()
        
        # Connect to database
        if not self.connect_database():
            return False
        
        # Start fetch session
        if not self.start_fetch_session():
            return False
        
        # Find resume point if enabled
        resume_token = self.find_resume_point()
        
        # Prepare API parameters
        params = {
            'format': 'json',
            'pageSize': self.config['batch_size']
        }
        
        # Add date filters only if specified (API may not support them)
        if self.config['data_start_date']:
            params['filter.dates.startFrom'] = self.config['data_start_date']
        if self.config['data_end_date']:
            params['filter.dates.startTo'] = self.config['data_end_date']
        
        next_page_token = resume_token
        page_count = 0
        
        try:
            while True:
                if self.shutdown_requested:
                    self.logger.info("🛑 Shutdown requested, stopping gracefully...")
                    self.update_fetch_log('interrupted', next_page_token)
                    break
                
                # Add page token if available
                if next_page_token:
                    params['pageToken'] = next_page_token
                elif 'pageToken' in params:
                    del params['pageToken']
                
                # Fetch data
                self.logger.info(f"📥 Fetching page {page_count + 1} (batch size: {self.config['batch_size']})")
                data = self.fetch_with_retry(self.config['api_base_url'], params)
                
                if not data:
                    self.logger.error("❌ Failed to fetch data")
                    self.update_fetch_log('failed')
                    break
                
                studies = data.get('studies', [])
                if not studies:
                    self.logger.info("📭 No more studies to fetch")
                    break
                
                # Process studies
                valid_studies = 0
                for study in studies:
                    if self.shutdown_requested:
                        break
                    
                    if self.validate_study_data(study):
                        if self.insert_or_update_study(study):
                            valid_studies += 1
                    else:
                        self.stats['errors'] += 1
                
                # Commit batch
                self.db_conn.commit()
                
                page_count += 1
                self.logger.info(f"✅ Processed {valid_studies}/{len(studies)} studies from page {page_count}")
                self.logger.info(f"📊 Progress: {self.stats['fetched']} fetched, {self.stats['updated']} updated, {self.stats['skipped']} skipped, {self.stats['errors']} errors")
                
                # Update fetch log periodically
                if page_count % 10 == 0:
                    self.update_fetch_log('running', next_page_token)
                
                # Check for next page
                next_page_token = data.get('nextPageToken')
                if not next_page_token:
                    self.logger.info("📄 Reached end of data")
                    break
                
                # Rate limiting
                time.sleep(self.config['api_rate_limit_delay'])
            
            # Final update
            if not self.shutdown_requested:
                self.update_fetch_log('completed')
                self.logger.info("🎉 Data collection completed successfully!")
            
        except Exception as e:
            self.logger.error(f"❌ Fatal error during fetch: {e}")
            self.update_fetch_log('failed')
        finally:
            self._cleanup()
        
        # Print final statistics
        self._print_final_stats()
    
    def _cleanup(self):
        """Clean up resources."""
        if self.db_cursor:
            self.db_cursor.close()
        if self.db_conn:
            self.db_conn.close()
        self.logger.info("🧹 Cleanup completed")
    
    def _print_final_stats(self):
        """Print final statistics."""
        if self.stats['start_time']:
            duration = time.time() - self.stats['start_time']
            self.logger.info("📈 Final Statistics:")
            self.logger.info(f"   Duration: {duration:.2f} seconds ({duration/60:.2f} minutes)")
            self.logger.info(f"   Studies fetched: {self.stats['fetched']:,}")
            self.logger.info(f"   Studies updated: {self.stats['updated']:,}")
            self.logger.info(f"   Studies skipped: {self.stats['skipped']:,}")
            self.logger.info(f"   Errors: {self.stats['errors']:,}")
            self.logger.info(f"   Total processed: {self.stats['fetched'] + self.stats['updated'] + self.stats['skipped']:,}")
            if duration > 0:
                rate = (self.stats['fetched'] + self.stats['updated']) / duration
                self.logger.info(f"   Processing rate: {rate:.2f} studies/second")

def main():
    """Main entry point."""
    fetcher = ClinicalTrialsFetcher()
    fetcher.fetch_all_studies()

if __name__ == "__main__":
    main()