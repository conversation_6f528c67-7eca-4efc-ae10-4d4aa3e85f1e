#!/usr/bin/env python3
"""
Generate validation report and statistics for clinical trials data.
This script analyzes the collected data and provides insights.
"""

import subprocess
import sys
import json
from datetime import datetime

def run_sql_query(query, description=""):
    """Run SQL query via Docker and return results."""
    try:
        result = subprocess.run([
            'docker', 'exec', 'trails-db-1',
            'psql', '-U', 'user', '-d', 'clinical_trials',
            '-c', query
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return True, result.stdout
        else:
            print(f"❌ SQL Error: {result.stderr}")
            return False, result.stderr
    except Exception as e:
        print(f"❌ Error running query: {e}")
        return False, str(e)

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def generate_summary_stats():
    """Generate basic summary statistics."""
    print_section("SUMMARY STATISTICS")
    
    # Total studies
    success, output = run_sql_query("SELECT COUNT(*) as total_studies FROM studies;")
    if success:
        lines = output.strip().split('\n')
        count_line = [line for line in lines if line.strip().isdigit()]
        if count_line:
            total_studies = int(count_line[0].strip())
            print(f"📊 Total Studies: {total_studies:,}")

    # Data size
    success, output = run_sql_query("""
        SELECT 
            pg_size_pretty(pg_total_relation_size('studies')) as table_size,
            pg_size_pretty(pg_database_size('clinical_trials')) as db_size;
    """)
    if success:
        print("💾 Database Size:")
        print(output)

    # Date ranges
    success, output = run_sql_query("""
        SELECT 
            MIN(fetch_date) as first_fetch,
            MAX(fetch_date) as last_fetch,
            COUNT(DISTINCT DATE(fetch_date)) as fetch_days
        FROM studies;
    """)
    if success:
        print("📅 Fetch Date Range:")
        print(output)

def analyze_study_characteristics():
    """Analyze study characteristics from JSON data."""
    print_section("STUDY CHARACTERISTICS")
    
    # Get sample of studies to analyze
    success, output = run_sql_query("""
        SELECT 
            nct_id,
            study_data->'protocolSection'->'identificationModule'->>'briefTitle' as brief_title,
            study_data->'protocolSection'->'statusModule'->>'overallStatus' as status,
            study_data->'protocolSection'->'designModule'->>'studyType' as study_type
        FROM studies 
        LIMIT 10;
    """)
    
    if success:
        print("📋 Sample Studies:")
        print(output)

def check_data_quality():
    """Check data quality and completeness."""
    print_section("DATA QUALITY ANALYSIS")
    
    # Check for required fields
    success, output = run_sql_query("""
        SELECT 
            COUNT(*) as total_studies,
            COUNT(study_data->'protocolSection'->'identificationModule'->>'nctId') as has_nct_id,
            COUNT(study_data->'protocolSection'->'identificationModule'->>'briefTitle') as has_brief_title,
            COUNT(study_data->'protocolSection'->'statusModule'->>'overallStatus') as has_status
        FROM studies;
    """)
    
    if success:
        print("✅ Required Fields Completeness:")
        print(output)

    # Check JSON structure validity
    success, output = run_sql_query("""
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN study_data ? 'protocolSection' THEN 1 END) as has_protocol_section,
            COUNT(CASE WHEN study_data->'protocolSection' ? 'identificationModule' THEN 1 END) as has_identification,
            COUNT(CASE WHEN study_data->'protocolSection' ? 'statusModule' THEN 1 END) as has_status_module
        FROM studies;
    """)
    
    if success:
        print("🔍 JSON Structure Completeness:")
        print(output)

def analyze_study_types():
    """Analyze different types of studies."""
    print_section("STUDY TYPE ANALYSIS")
    
    # Study types
    success, output = run_sql_query("""
        SELECT 
            study_data->'protocolSection'->'designModule'->>'studyType' as study_type,
            COUNT(*) as count
        FROM studies 
        GROUP BY study_data->'protocolSection'->'designModule'->>'studyType'
        ORDER BY count DESC;
    """)
    
    if success:
        print("📊 Study Types:")
        print(output)

    # Study phases (for interventional studies)
    success, output = run_sql_query("""
        SELECT 
            jsonb_array_elements_text(study_data->'protocolSection'->'designModule'->'phases') as phase,
            COUNT(*) as count
        FROM studies 
        WHERE study_data->'protocolSection'->'designModule' ? 'phases'
        GROUP BY phase
        ORDER BY count DESC;
    """)
    
    if success:
        print("🔬 Study Phases:")
        print(output)

def analyze_study_status():
    """Analyze study status distribution."""
    print_section("STUDY STATUS ANALYSIS")
    
    success, output = run_sql_query("""
        SELECT 
            study_data->'protocolSection'->'statusModule'->>'overallStatus' as status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
        FROM studies 
        GROUP BY study_data->'protocolSection'->'statusModule'->>'overallStatus'
        ORDER BY count DESC;
    """)
    
    if success:
        print("📈 Overall Status Distribution:")
        print(output)

def analyze_sponsors():
    """Analyze study sponsors."""
    print_section("SPONSOR ANALYSIS")
    
    success, output = run_sql_query("""
        SELECT 
            study_data->'protocolSection'->'sponsorCollaboratorsModule'->'leadSponsor'->>'name' as sponsor,
            COUNT(*) as count
        FROM studies 
        GROUP BY study_data->'protocolSection'->'sponsorCollaboratorsModule'->'leadSponsor'->>'name'
        ORDER BY count DESC
        LIMIT 10;
    """)
    
    if success:
        print("🏢 Top Sponsors:")
        print(output)

def check_conditions():
    """Analyze medical conditions studied."""
    print_section("MEDICAL CONDITIONS ANALYSIS")
    
    success, output = run_sql_query("""
        SELECT 
            jsonb_array_elements_text(study_data->'protocolSection'->'conditionsModule'->'conditions') as condition,
            COUNT(*) as count
        FROM studies 
        WHERE study_data->'protocolSection'->'conditionsModule' ? 'conditions'
        GROUP BY condition
        ORDER BY count DESC
        LIMIT 10;
    """)
    
    if success:
        print("🏥 Top Medical Conditions:")
        print(output)

def fetch_log_analysis():
    """Analyze fetch log data."""
    print_section("FETCH LOG ANALYSIS")
    
    success, output = run_sql_query("""
        SELECT 
            id,
            fetch_start,
            fetch_end,
            status,
            studies_fetched,
            studies_updated,
            studies_skipped,
            EXTRACT(EPOCH FROM (fetch_end - fetch_start))/60 as duration_minutes
        FROM fetch_log 
        ORDER BY fetch_start DESC;
    """)
    
    if success:
        print("📋 Recent Fetch Sessions:")
        print(output)

def performance_analysis():
    """Analyze system performance."""
    print_section("PERFORMANCE ANALYSIS")
    
    # Index usage
    success, output = run_sql_query("""
        SELECT 
            indexname,
            idx_scan as scans,
            idx_tup_read as tuples_read,
            idx_tup_fetch as tuples_fetched
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public'
        ORDER BY idx_scan DESC;
    """)
    
    if success:
        print("🚀 Index Usage Statistics:")
        print(output)

def generate_recommendations():
    """Generate recommendations based on analysis."""
    print_section("RECOMMENDATIONS")
    
    # Get basic stats for recommendations
    success, output = run_sql_query("SELECT COUNT(*) FROM studies;")
    if success:
        lines = output.strip().split('\n')
        count_line = [line for line in lines if line.strip().isdigit()]
        if count_line:
            total_studies = int(count_line[0].strip())
            
            print("💡 Analysis-Based Recommendations:")
            print()
            
            if total_studies < 100:
                print("🟡 PILOT PHASE - Small dataset detected")
                print("   • Current data: Good for testing and validation")
                print("   • Next step: Run full production collection")
                print("   • Estimated full dataset: 500K+ studies for 5 years")
            elif total_studies < 10000:
                print("🟡 PARTIAL COLLECTION - Moderate dataset")
                print("   • Consider optimizing batch sizes")
                print("   • Monitor API rate limits")
                print("   • Validate data quality regularly")
            else:
                print("🟢 FULL COLLECTION - Large dataset")
                print("   • Monitor database performance")
                print("   • Consider data archiving strategies")
                print("   • Implement regular data validation")
            
            print()
            print("📋 General Recommendations:")
            print("   • Regularly backup the database")
            print("   • Monitor disk space usage")
            print("   • Validate data quality after each major collection")
            print("   • Consider implementing data retention policies")

def main():
    """Generate comprehensive validation report."""
    print("📊 Clinical Trials Data - Validation Report")
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if database is accessible
    success, _ = run_sql_query("SELECT 1;")
    if not success:
        print("❌ Cannot connect to database. Make sure Docker container is running.")
        sys.exit(1)
    
    # Run all analyses
    try:
        generate_summary_stats()
        analyze_study_characteristics()
        check_data_quality()
        analyze_study_types()
        analyze_study_status()
        analyze_sponsors()
        check_conditions()
        fetch_log_analysis()
        performance_analysis()
        generate_recommendations()
        
        print_section("REPORT COMPLETE")
        print("🎉 Validation report generated successfully!")
        print("\nTo export this report to a file:")
        print("python3 generate_report.py > validation_report.txt")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Report generation interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error generating report: {e}")

if __name__ == "__main__":
    main()