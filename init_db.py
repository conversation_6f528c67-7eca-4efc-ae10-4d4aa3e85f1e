#!/usr/bin/env python3
"""
Database initialization script for Clinical Trials data collection.
This script creates the database schema and provides utility functions.
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('db_init.log')
    ]
)
logger = logging.getLogger(__name__)

def get_db_config():
    """Get database configuration from environment variables or defaults."""
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', '5432'),
        'database': os.getenv('DB_NAME', 'clinical_trials'),
        'user': os.getenv('DB_USER', 'user'),
        'password': os.getenv('DB_PASSWORD', 'password')
    }

def test_connection(config):
    """Test database connection."""
    try:
        logger.info("Testing database connection...")
        conn = psycopg2.connect(**config)
        conn.close()
        logger.info("✅ Database connection successful")
        return True
    except psycopg2.Error as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

def create_database_if_not_exists(config):
    """Create database if it doesn't exist."""
    try:
        # Connect to postgres database to create our database
        postgres_config = config.copy()
        postgres_config['database'] = 'postgres'
        
        conn = psycopg2.connect(**postgres_config)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cur = conn.cursor()
        
        # Check if database exists
        cur.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (config['database'],))
        exists = cur.fetchone()
        
        if not exists:
            logger.info(f"Creating database '{config['database']}'...")
            cur.execute(f"CREATE DATABASE {config['database']}")
            logger.info(f"✅ Database '{config['database']}' created successfully")
        else:
            logger.info(f"Database '{config['database']}' already exists")
        
        cur.close()
        conn.close()
        return True
        
    except psycopg2.Error as e:
        logger.error(f"❌ Error creating database: {e}")
        return False

def execute_schema(config):
    """Execute the schema SQL file."""
    try:
        logger.info("Executing schema SQL...")
        
        # Read schema file
        schema_path = Path(__file__).parent / 'schema.sql'
        if not schema_path.exists():
            logger.error(f"❌ Schema file not found: {schema_path}")
            return False
        
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
        
        # Execute schema
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Execute the entire schema
        cur.execute(schema_sql)
        conn.commit()
        
        cur.close()
        conn.close()
        
        logger.info("✅ Schema executed successfully")
        return True
        
    except psycopg2.Error as e:
        logger.error(f"❌ Error executing schema: {e}")
        return False
    except FileNotFoundError as e:
        logger.error(f"❌ Schema file not found: {e}")
        return False

def verify_schema(config):
    """Verify that the schema was created correctly."""
    try:
        logger.info("Verifying schema...")
        
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Check if main tables exist
        tables_to_check = ['studies', 'fetch_log']
        for table in tables_to_check:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table,))
            exists = cur.fetchone()[0]
            if exists:
                logger.info(f"✅ Table '{table}' exists")
            else:
                logger.error(f"❌ Table '{table}' not found")
                return False
        
        # Check if indexes exist
        cur.execute("""
            SELECT indexname FROM pg_indexes 
            WHERE tablename IN ('studies', 'fetch_log')
            ORDER BY indexname;
        """)
        indexes = cur.fetchall()
        logger.info(f"✅ Found {len(indexes)} indexes")
        
        # Check if views exist
        views_to_check = ['study_summary', 'status_summary', 'fetch_summary']
        for view in views_to_check:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.views 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (view,))
            exists = cur.fetchone()[0]
            if exists:
                logger.info(f"✅ View '{view}' exists")
            else:
                logger.error(f"❌ View '{view}' not found")
                return False
        
        cur.close()
        conn.close()
        
        logger.info("✅ Schema verification complete")
        return True
        
    except psycopg2.Error as e:
        logger.error(f"❌ Error verifying schema: {e}")
        return False

def get_database_stats(config):
    """Get basic database statistics."""
    try:
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Get table sizes
        cur.execute("""
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
        """)
        table_sizes = cur.fetchall()
        
        # Get study count
        cur.execute("SELECT COUNT(*) FROM studies;")
        study_count = cur.fetchone()[0]
        
        # Get fetch log count
        cur.execute("SELECT COUNT(*) FROM fetch_log;")
        log_count = cur.fetchone()[0]
        
        logger.info("📊 Database Statistics:")
        logger.info(f"   Studies: {study_count:,}")
        logger.info(f"   Fetch logs: {log_count}")
        logger.info("   Table sizes:")
        for schema, table, size in table_sizes:
            logger.info(f"     {table}: {size}")
        
        cur.close()
        conn.close()
        
    except psycopg2.Error as e:
        logger.error(f"❌ Error getting database stats: {e}")

def reset_database(config, force=False):
    """Reset the database (drop and recreate schema)."""
    if not force:
        response = input("⚠️  This will drop all existing data. Are you sure? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("Reset cancelled")
            return False
    
    try:
        logger.info("Resetting database...")
        
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Drop all tables and views
        cur.execute("""
            DROP TABLE IF EXISTS studies CASCADE;
            DROP TABLE IF EXISTS fetch_log CASCADE;
        """)
        conn.commit()
        
        cur.close()
        conn.close()
        
        logger.info("✅ Database reset complete")
        
        # Re-create schema
        return execute_schema(config)
        
    except psycopg2.Error as e:
        logger.error(f"❌ Error resetting database: {e}")
        return False

def main():
    """Main initialization function."""
    logger.info("🚀 Starting database initialization...")
    
    config = get_db_config()
    logger.info(f"Database config: {config['host']}:{config['port']}/{config['database']}")
    
    # Check if we should reset
    reset = '--reset' in sys.argv or '--force-reset' in sys.argv
    force_reset = '--force-reset' in sys.argv
    
    if reset:
        if not reset_database(config, force=force_reset):
            sys.exit(1)
    else:
        # Create database if needed
        if not create_database_if_not_exists(config):
            sys.exit(1)
        
        # Test connection
        if not test_connection(config):
            sys.exit(1)
        
        # Execute schema
        if not execute_schema(config):
            sys.exit(1)
        
        # Verify schema
        if not verify_schema(config):
            sys.exit(1)
    
    # Show stats
    if '--stats' in sys.argv:
        get_database_stats(config)
    
    logger.info("🎉 Database initialization complete!")

if __name__ == "__main__":
    main()