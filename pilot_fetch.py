
import requests
import json
import subprocess
import time
from datetime import datetime

def run_pilot():
    print("🔄 Starting pilot data collection...")
    
    api_url = "https://clinicaltrials.gov/api/v2/studies"
    params = {
        'format': 'json',
        'pageSize': 10
    }
    
    studies_processed = 0
    page_count = 0
    
    try:
        response = requests.get(api_url, params=params, timeout=30)
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return False
        
        data = response.json()
        studies = data.get('studies', [])
        
        print(f"📥 Retrieved {len(studies)} studies from API")
        
        for study in studies:
            try:
                nct_id = study['protocolSection']['identificationModule']['nctId']
                study_json = json.dumps(study)
                
                # Insert study via Docker exec
                cmd = [
                    'docker', 'exec', 'trails-db-1',
                    'psql', '-U', 'user', '-d', 'clinical_trials',
                    '-c', f"INSERT INTO studies (nct_id, study_data) VALUES ('{nct_id}', '{study_json.replace("'", "''")}') ON CONFLICT (nct_id) DO NOTHING;"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    studies_processed += 1
                    print(f"✅ Inserted study {nct_id}")
                else:
                    print(f"❌ Failed to insert {nct_id}: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ Error processing study: {e}")
        
        print(f"🎉 Pilot test completed: {studies_processed}/{len(studies)} studies processed")
        return True
        
    except Exception as e:
        print(f"❌ Pilot test failed: {e}")
        return False

if __name__ == "__main__":
    success = run_pilot()
    exit(0 if success else 1)
