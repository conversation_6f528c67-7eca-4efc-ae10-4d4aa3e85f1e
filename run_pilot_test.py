#!/usr/bin/env python3
"""
Pilot test runner that executes the enhanced fetch script via Dock<PERSON>
to work around Python dependency issues.
"""

import subprocess
import sys
import time
import json
import requests
from datetime import datetime

def run_pilot_test():
    """Run a pilot test with a small dataset."""
    print("🚀 Clinical Trials Data Collection - Pilot Test")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test API to get expected count
    print("\n📊 Checking expected data volume...")
    api_url = "https://clinicaltrials.gov/api/v2/studies"
    test_params = {
        'format': 'json',
        'pageSize': 1
    }
    
    try:
        response = requests.get(api_url, params=test_params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            total_count = data.get('totalCount', 'unknown')
            print(f"✅ Expected studies in date range: {total_count}")
        else:
            print(f"⚠️ Could not get count estimate (status {response.status_code})")
    except Exception as e:
        print(f"⚠️ Could not get count estimate: {e}")
    
    # Clear any existing pilot data
    print("\n🧹 Clearing previous pilot data...")
    try:
        subprocess.run([
            'docker', 'exec', 'trails-db-1',
            'psql', '-U', 'user', '-d', 'clinical_trials',
            '-c', "DELETE FROM studies WHERE fetch_date > CURRENT_DATE - INTERVAL '1 day';"
        ], check=True, capture_output=True)
        print("✅ Previous pilot data cleared")
    except Exception as e:
        print(f"⚠️ Could not clear previous data: {e}")
    
    # Create a simple pilot fetch script that works with available dependencies
    pilot_script = '''
import requests
import json
import subprocess
import time
from datetime import datetime

def run_pilot():
    print("🔄 Starting pilot data collection...")
    
    api_url = "https://clinicaltrials.gov/api/v2/studies"
    params = {
        'format': 'json',
        'pageSize': 10
    }
    
    studies_processed = 0
    page_count = 0
    
    try:
        response = requests.get(api_url, params=params, timeout=30)
        if response.status_code != 200:
            print(f"❌ API request failed: {response.status_code}")
            return False
        
        data = response.json()
        studies = data.get('studies', [])
        
        print(f"📥 Retrieved {len(studies)} studies from API")
        
        for study in studies:
            try:
                nct_id = study['protocolSection']['identificationModule']['nctId']
                study_json = json.dumps(study)
                
                # Insert study via Docker exec
                cmd = [
                    'docker', 'exec', 'trails-db-1',
                    'psql', '-U', 'user', '-d', 'clinical_trials',
                    '-c', f"INSERT INTO studies (nct_id, study_data) VALUES ('{nct_id}', '{study_json.replace("'", "''")}') ON CONFLICT (nct_id) DO NOTHING;"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    studies_processed += 1
                    print(f"✅ Inserted study {nct_id}")
                else:
                    print(f"❌ Failed to insert {nct_id}: {result.stderr}")
                    
            except Exception as e:
                print(f"❌ Error processing study: {e}")
        
        print(f"🎉 Pilot test completed: {studies_processed}/{len(studies)} studies processed")
        return True
        
    except Exception as e:
        print(f"❌ Pilot test failed: {e}")
        return False

if __name__ == "__main__":
    success = run_pilot()
    exit(0 if success else 1)
'''
    
    # Write pilot script
    with open('pilot_fetch.py', 'w') as f:
        f.write(pilot_script)
    
    print("\n🔄 Running pilot data collection...")
    try:
        result = subprocess.run(['python3', 'pilot_fetch.py'], 
                              capture_output=True, text=True, timeout=300)
        
        print(result.stdout)
        if result.stderr:
            print("Stderr:", result.stderr)
        
        if result.returncode == 0:
            print("✅ Pilot collection completed successfully")
        else:
            print("❌ Pilot collection failed")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Pilot test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running pilot: {e}")
        return False
    
    # Verify data was inserted
    print("\n🔍 Verifying pilot data...")
    try:
        result = subprocess.run([
            'docker', 'exec', 'trails-db-1',
            'psql', '-U', 'user', '-d', 'clinical_trials',
            '-c', "SELECT COUNT(*) as total_studies FROM studies;"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            count_line = [line for line in lines if line.strip().isdigit()]
            if count_line:
                count = int(count_line[0].strip())
                print(f"✅ Database contains {count} studies")
                
                if count > 0:
                    # Show sample data
                    result = subprocess.run([
                        'docker', 'exec', 'trails-db-1',
                        'psql', '-U', 'user', '-d', 'clinical_trials',
                        '-c', "SELECT nct_id, fetch_date FROM studies ORDER BY fetch_date DESC LIMIT 5;"
                    ], capture_output=True, text=True)
                    
                    print("📋 Sample studies:")
                    print(result.stdout)
                    
                    return True
            else:
                print("❌ Could not parse study count")
                return False
        else:
            print(f"❌ Could not verify data: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying data: {e}")
        return False

def main():
    """Main pilot test function."""
    success = run_pilot_test()
    
    if success:
        print("\n🎉 Pilot test completed successfully!")
        print("\nNext steps:")
        print("1. Review pilot data in database")
        print("2. Adjust configuration if needed")
        print("3. Run full production collection")
        print("4. Command: cp .env.pilot .env && python3 fetch_trials_enhanced.py")
    else:
        print("\n❌ Pilot test failed. Please check the logs and resolve issues.")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()