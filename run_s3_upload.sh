#!/bin/bash
# Robust S3 upload runner with automatic recovery

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
LOG_FILE="$SCRIPT_DIR/s3_upload.log"
PID_FILE="$SCRIPT_DIR/s3_upload.pid"
PROFILE="${AWS_PROFILE:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}S3 Upload Runner${NC}"
echo "================="
echo ""

# Function to check if upload is already running
check_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo -e "${YELLOW}Upload is already running (PID: $PID)${NC}"
            echo "To monitor: tail -f $LOG_FILE"
            return 0
        else
            echo "Found stale PID file, removing..."
            rm "$PID_FILE"
        fi
    fi
    return 1
}

# Function to start upload
start_upload() {
    echo "Starting S3 upload process..."
    echo "Log file: $LOG_FILE"
    echo ""
    
    # Build command
    CMD="python3 $SCRIPT_DIR/upload_to_s3.py"
    
    if [ ! -z "$PROFILE" ]; then
        CMD="$CMD --profile $PROFILE"
        echo "Using AWS profile: $PROFILE"
    fi
    
    # Add other arguments passed to script
    if [ $# -gt 0 ]; then
        CMD="$CMD $@"
    fi
    
    # Start upload in background with nohup
    echo "Running: $CMD"
    nohup $CMD >> "$LOG_FILE" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    
    echo -e "${GREEN}Upload started (PID: $PID)${NC}"
    echo ""
    echo "Commands:"
    echo "  Monitor progress:  tail -f $LOG_FILE"
    echo "  Check status:      ./run_s3_upload.sh status"
    echo "  Stop upload:       ./run_s3_upload.sh stop"
    echo ""
}

# Function to stop upload
stop_upload() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "Stopping upload process (PID: $PID)..."
            kill -TERM $PID
            sleep 2
            if ps -p $PID > /dev/null 2>&1; then
                echo "Process didn't stop gracefully, forcing..."
                kill -KILL $PID
            fi
            rm "$PID_FILE"
            echo -e "${GREEN}Upload stopped${NC}"
        else
            echo "Upload process not running"
            rm "$PID_FILE"
        fi
    else
        echo "No upload process found"
    fi
}

# Function to show status
show_status() {
    if check_running; then
        echo ""
        echo "Recent log entries:"
        echo "-------------------"
        tail -n 20 "$LOG_FILE"
        echo ""
        
        # Show progress if available
        if [ -f "s3_upload_progress.json" ]; then
            echo "Upload progress:"
            echo "----------------"
            python3 -c "
import json
with open('s3_upload_progress.json', 'r') as f:
    data = json.load(f)
    stats = data.get('stats', {})
    print(f\"Uploaded: {stats.get('uploaded', 0):,}\")
    print(f\"Skipped: {stats.get('skipped', 0):,}\")
    print(f\"Errors: {stats.get('errors', 0):,}\")
    print(f\"Last update: {data.get('last_update', 'Unknown')}\")
"
        fi
    else
        echo "Upload is not running"
    fi
}

# Function for automatic retry on failure
auto_retry() {
    MAX_RETRIES=5
    RETRY_COUNT=0
    RETRY_DELAY=60  # seconds
    
    echo "Starting upload with automatic retry (max $MAX_RETRIES retries)..."
    
    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if check_running; then
            echo "Upload is already running"
            break
        fi
        
        echo "Attempt $((RETRY_COUNT + 1)) of $MAX_RETRIES"
        start_upload "$@"
        
        # Wait for process to start
        sleep 5
        
        # Monitor process
        PID=$(cat "$PID_FILE" 2>/dev/null)
        if [ ! -z "$PID" ]; then
            while ps -p $PID > /dev/null 2>&1; do
                sleep 30
            done
            
            # Check if it completed successfully
            if grep -q "Upload complete!" "$LOG_FILE"; then
                echo -e "${GREEN}Upload completed successfully!${NC}"
                break
            else
                echo -e "${YELLOW}Upload failed or interrupted${NC}"
                RETRY_COUNT=$((RETRY_COUNT + 1))
                if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                    echo "Waiting $RETRY_DELAY seconds before retry..."
                    sleep $RETRY_DELAY
                fi
            fi
        fi
    done
    
    if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
        echo -e "${RED}Upload failed after $MAX_RETRIES attempts${NC}"
        echo "Check log file for details: $LOG_FILE"
        exit 1
    fi
}

# Main command handling
case "${1:-start}" in
    start)
        if check_running; then
            exit 0
        fi
        shift
        start_upload "$@"
        ;;
    stop)
        stop_upload
        ;;
    status)
        show_status
        ;;
    restart)
        stop_upload
        sleep 2
        shift
        start_upload "$@"
        ;;
    auto)
        shift
        auto_retry "$@"
        ;;
    logs)
        tail -f "$LOG_FILE"
        ;;
    *)
        echo "Usage: $0 {start|stop|status|restart|auto|logs} [upload options]"
        echo ""
        echo "Commands:"
        echo "  start   - Start upload process in background"
        echo "  stop    - Stop running upload"
        echo "  status  - Show upload status and progress"
        echo "  restart - Stop and start upload"
        echo "  auto    - Start with automatic retry on failure"
        echo "  logs    - Follow log output"
        echo ""
        echo "Upload options:"
        echo "  --dry-run          - Show what would be uploaded"
        echo "  --max-workers N    - Number of parallel uploads (default: 10)"
        echo "  --verify           - Verify uploads after completion"
        echo "  --reset            - Reset progress and start fresh"
        echo ""
        echo "Example:"
        echo "  $0 start --max-workers 20"
        echo "  $0 auto              # Start with auto-retry"
        exit 1
        ;;
esac