-- Clinical Trials Database Schema
-- This schema stores complete clinical trial data from ClinicalTrials.gov API
-- with both full JSON preservation and extracted key fields for efficient querying

-- Drop tables if they exist (for clean re-creation)
DROP TABLE IF EXISTS studies CASCADE;
DROP TABLE IF EXISTS fetch_log CASCADE;

-- Main studies table storing complete trial data
CREATE TABLE studies (
    id SERIAL PRIMARY KEY,
    nct_id VARCHAR(11) UNIQUE NOT NULL, -- NCT ID (NCT + 8 digits)
    
    -- Key extracted fields for efficient querying
    brief_title TEXT,
    official_title TEXT,
    overall_status VARCHAR(50),
    study_type VARCHAR(50),
    phase VARCHAR(50),
    
    -- Important dates
    start_date DATE,
    start_date_type VARCHAR(20),
    primary_completion_date DATE,
    primary_completion_date_type VARCHAR(20),
    completion_date DATE,
    completion_date_type VARCHAR(20),
    study_first_submit_date DATE,
    study_first_posted_date DATE,
    last_update_submit_date DATE,
    last_update_posted_date DATE,
    
    -- Organization info
    sponsor_name TEXT,
    responsible_party_type VARCHAR(50),
    
    -- Study characteristics
    enrollment_count INTEGER,
    enrollment_type VARCHAR(20),
    number_of_arms INTEGER,
    
    -- Eligibility
    minimum_age VARCHAR(20),
    maximum_age VARCHAR(20),
    sex VARCHAR(20),
    accepts_healthy_volunteers BOOLEAN,
    
    -- Geographic info
    has_us_facility BOOLEAN,
    has_international_facility BOOLEAN,
    
    -- Results availability
    has_results BOOLEAN,
    has_expanded_access BOOLEAN,
    
    -- Complete JSON data (preserves all information)
    study_data JSONB NOT NULL,
    
    -- Metadata
    fetch_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Add constraint to ensure NCT ID format
    CONSTRAINT valid_nct_id CHECK (nct_id ~ '^NCT[0-9]{8}$')
);

-- Fetch log table to track data collection progress
CREATE TABLE fetch_log (
    id SERIAL PRIMARY KEY,
    fetch_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fetch_end TIMESTAMP,
    status VARCHAR(20) DEFAULT 'running', -- running, completed, failed, interrupted
    studies_fetched INTEGER DEFAULT 0,
    studies_updated INTEGER DEFAULT 0,
    studies_skipped INTEGER DEFAULT 0,
    total_studies INTEGER,
    last_page_token TEXT,
    error_message TEXT,
    filter_params JSONB, -- Store the API filter parameters used
    notes TEXT
);

-- Indexes for efficient querying

-- Primary lookup index
CREATE INDEX idx_studies_nct_id ON studies(nct_id);

-- Status and type indexes
CREATE INDEX idx_studies_overall_status ON studies(overall_status);
CREATE INDEX idx_studies_study_type ON studies(study_type);
CREATE INDEX idx_studies_phase ON studies(phase);

-- Date indexes for temporal queries
CREATE INDEX idx_studies_start_date ON studies(start_date);
CREATE INDEX idx_studies_completion_date ON studies(completion_date);
CREATE INDEX idx_studies_submit_date ON studies(study_first_submit_date);
CREATE INDEX idx_studies_fetch_date ON studies(fetch_date);

-- Sponsor and organization indexes
CREATE INDEX idx_studies_sponsor_name ON studies(sponsor_name);

-- Composite indexes for common queries
CREATE INDEX idx_studies_status_type ON studies(overall_status, study_type);
CREATE INDEX idx_studies_dates_status ON studies(start_date, overall_status);

-- Geographic indexes
CREATE INDEX idx_studies_us_facility ON studies(has_us_facility);

-- JSONB indexes for flexible querying of the complete data
CREATE INDEX idx_studies_data_gin ON studies USING GIN(study_data);

-- Specific JSONB path indexes for common queries
CREATE INDEX idx_studies_conditions ON studies USING GIN((study_data->'protocolSection'->'conditionsModule'->'conditions'));
CREATE INDEX idx_studies_interventions ON studies USING GIN((study_data->'protocolSection'->'armsInterventionsModule'->'interventions'));
CREATE INDEX idx_studies_keywords ON studies USING GIN((study_data->'protocolSection'->'conditionsModule'->'keywords'));

-- Fetch log indexes
CREATE INDEX idx_fetch_log_status ON fetch_log(status);
CREATE INDEX idx_fetch_log_start ON fetch_log(fetch_start);

-- Function to update the last_updated timestamp
CREATE OR REPLACE FUNCTION update_last_updated_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update last_updated on record changes
CREATE TRIGGER update_studies_last_updated 
    BEFORE UPDATE ON studies 
    FOR EACH ROW 
    EXECUTE FUNCTION update_last_updated_column();

-- Useful views for common queries

-- View for basic study information
CREATE VIEW study_summary AS
SELECT 
    nct_id,
    brief_title,
    official_title,
    overall_status,
    study_type,
    phase,
    start_date,
    completion_date,
    sponsor_name,
    enrollment_count,
    has_results,
    fetch_date
FROM studies
ORDER BY start_date DESC;

-- View for study counts by status
CREATE VIEW status_summary AS
SELECT 
    overall_status,
    COUNT(*) as count,
    COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as percentage
FROM studies 
GROUP BY overall_status
ORDER BY count DESC;

-- View for recent fetch statistics
CREATE VIEW fetch_summary AS
SELECT 
    fetch_start,
    fetch_end,
    status,
    studies_fetched,
    studies_updated,
    EXTRACT(EPOCH FROM (fetch_end - fetch_start))/60 as duration_minutes
FROM fetch_log 
ORDER BY fetch_start DESC;

-- Comments for documentation
COMMENT ON TABLE studies IS 'Main table storing clinical trial data from ClinicalTrials.gov';
COMMENT ON COLUMN studies.study_data IS 'Complete JSON response from ClinicalTrials.gov API';
COMMENT ON COLUMN studies.nct_id IS 'Unique NCT identifier (NCT + 8 digits)';
COMMENT ON TABLE fetch_log IS 'Log of data collection runs for tracking and resumption';

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON studies TO clinical_trials_user;
-- GRANT SELECT, INSERT, UPDATE ON fetch_log TO clinical_trials_user;
-- GRANT USAGE ON SEQUENCE studies_id_seq TO clinical_trials_user;
-- GRANT USAGE ON SEQUENCE fetch_log_id_seq TO clinical_trials_user;