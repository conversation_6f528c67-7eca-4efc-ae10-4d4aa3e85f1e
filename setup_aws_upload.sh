#!/bin/bash
# Setup script for AWS S3 uploads with long-running credential support

echo "AWS S3 Upload Setup"
echo "=================="
echo ""
echo "This script will help you configure AWS credentials for long-running uploads."
echo ""

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "Error: AWS CLI is not installed. Please install it first:"
    echo "  pip install awscli"
    exit 1
fi

echo "Choose your authentication method:"
echo "1. IAM Role (EC2/ECS/Lambda) - Best for cloud environments"
echo "2. AWS SSO (AWS Identity Center) - Best for developer laptops"
echo "3. IAM User with long-lived credentials - Traditional method"
echo "4. Assume Role with STS - For cross-account access"
echo ""
read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo ""
        echo "IAM Role Setup"
        echo "--------------"
        echo "If you're running on EC2/ECS/Lambda, no additional setup needed!"
        echo "The SDK will automatically use the instance/task role."
        echo ""
        echo "To test: aws sts get-caller-identity"
        ;;
    2)
        echo ""
        echo "AWS SSO Setup"
        echo "-------------"
        echo "Setting up AWS SSO for automatic credential refresh..."
        echo ""
        read -p "Enter your SSO start URL: " sso_url
        read -p "Enter your SSO region: " sso_region
        read -p "Enter your SSO account ID: " account_id
        read -p "Enter your SSO role name: " role_name
        read -p "Enter a profile name (e.g., 'clinical-trials'): " profile_name
        
        # Configure SSO
        aws configure sso --profile $profile_name
        
        echo ""
        echo "SSO configured! To use it:"
        echo "1. Login: aws sso login --profile $profile_name"
        echo "2. Run upload: python3 upload_to_s3.py --profile $profile_name"
        echo ""
        echo "Note: SSO credentials auto-refresh during long uploads!"
        ;;
    3)
        echo ""
        echo "IAM User Setup"
        echo "--------------"
        echo "Setting up long-lived IAM credentials..."
        echo ""
        aws configure
        echo ""
        echo "Credentials configured in default profile."
        echo "Warning: These credentials may expire if using temporary tokens."
        ;;
    4)
        echo ""
        echo "Assume Role Setup"
        echo "-----------------"
        echo "Setting up role assumption for cross-account access..."
        echo ""
        read -p "Enter the role ARN to assume: " role_arn
        read -p "Enter session name: " session_name
        read -p "Enter profile name for assumed role: " profile_name
        
        # Create a profile that assumes the role
        aws configure set role_arn $role_arn --profile $profile_name
        aws configure set source_profile default --profile $profile_name
        aws configure set role_session_name $session_name --profile $profile_name
        
        echo ""
        echo "Assume role configured!"
        echo "Run upload: python3 upload_to_s3.py --profile $profile_name"
        ;;
esac

echo ""
echo "Testing S3 access..."
echo ""

# Test S3 access
if [ ! -z "$profile_name" ]; then
    aws s3 ls s3://trialynx-clinical-trials-gov/ --profile $profile_name 2>&1 | head -5
else
    aws s3 ls s3://trialynx-clinical-trials-gov/ 2>&1 | head -5
fi

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ S3 access confirmed!"
else
    echo ""
    echo "❌ S3 access failed. Please check your credentials and permissions."
fi

echo ""
echo "For long-running uploads (500k+ files), we recommend:"
echo "1. Using tmux or screen to maintain the session"
echo "2. Running with: nohup python3 upload_to_s3.py > upload.log 2>&1 &"
echo "3. Monitor progress: tail -f upload.log"
echo ""