Protocol Section
Field Name
Piece Name	Field Title	Alt Piece Names	Classic Type	Data Type	Definition	Description	Notes	Index Field
Classic XPath
protocolSection
ProtocolSection	Protocol Section	

	STRUCT	ProtocolSection	Study Protocol			protocolSection
/Study/ProtocolSection
identificationModule
IdentificationModule	Identification Module	

	STRUCT	IdentificationModule	Study Identification			protocolSection.identificationModule
/Study/ProtocolSection/IdentificationModule
nctId
NCTId	National Clinical Trial (NCT) Identification Number	

    NCT-ID 

	TEXT (max 11 chars)	text (stats)		
The unique identification code given to each clinical study upon registration at ClinicalTrials.gov. The format is "NCT" followed by an 8-digit number. Also known as ClinicalTrials.gov Identifier
	Generated by PRS when a study is published initially	protocolSection.identificationModule.nctId
/Study/ProtocolSection/IdentificationModule/NCTId
nctIdAliases
NCTIdAlias	Obsolete or duplicate NCT that's associated with a published NCT	

	TEXT	text[] ✓ (stats)		
Identifier(s) that are considered "Obsolete" or "Duplicate". No study is displayed on public site. Request is redirected/forwarded to another NCT Identifier
		protocolSection.identificationModule.nctIdAliases
/Study/ProtocolSection/IdentificationModule/NCTIdAliasList/NCTIdAlias
numNctAliases ✗
NumNCTAliases	Number of obsolete NCT's associated with a published NCT	

	FUNC NOccrs(NCTIdAlias)	short (stats)		
Number of obsolete identifiers for a NCTId
		protocolSection.identificationModule.numNctAliases
/Study/ProtocolSection/IdentificationModule/NumNCTAliases
orgStudyIdInfo
OrgStudyIdInfo		

	STRUCT	OrgStudyIdInfo				protocolSection.identificationModule.orgStudyIdInfo
/Study/ProtocolSection/IdentificationModule/OrgStudyIdInfo
id
OrgStudyId	Organization's Unique Protocol Identification Number	

    ORGANIZATION-STUDY-ID 

	TEXT (max 30 chars)	text ✓ (stats)	Unique Protocol Identification Number		Required for INT/OBS/EA. DP can enter "Unknown" or "TBD" and Ingest strips those value and marks them as [Missing]	protocolSection.identificationModule.orgStudyIdInfo.id
/Study/ProtocolSection/IdentificationModule/OrgStudyIdInfo/OrgStudyId
type
OrgStudyIdType	Organization Id Type	

    ORGANIZATION-STUDY-ID-TYPE 

	TEXT	enum OrgStudyIdType (stats)		
Type of organization's unique protocol ID
		protocolSection.identificationModule.orgStudyIdInfo.type
/Study/ProtocolSection/IdentificationModule/OrgStudyIdInfo/OrgStudyIdType
link
OrgStudyIdLink	Organization ID Link	

	TEXT	text (stats)		
URL link based on OrgStudyId and OrgStudyIdType input in PRS, include system-generated links to NIH RePORTER, specifically (associated with the types of federal funding identified as OrgStudyIdType)
		protocolSection.identificationModule.orgStudyIdInfo.link
/Study/ProtocolSection/IdentificationModule/OrgStudyIdInfo/OrgStudyIdLink
secondaryIdInfos
SecondaryIdInfo		

	STRUCT	SecondaryIdInfo[]				protocolSection.identificationModule.secondaryIdInfos
/Study/ProtocolSection/IdentificationModule/SecondaryIdInfoList/SecondaryIdInfo
id
SecondaryId	Secondary ID	

    SECONDARY-ID 

	TEXT (max 30 chars)	text ✓ (stats)	Secondary IDs		If the clinical study is funded in whole or in part by a U.S. Federal Government agency, the complete grant or contract number must be submitted as a Secondary ID.
Optional if Expanded Access Type is "Individual Patients"	protocolSection.identificationModule.secondaryIdInfos.id
/Study/ProtocolSection/IdentificationModule/SecondaryIdInfoList/SecondaryIdInfo/SecondaryId
type
SecondaryIdType	Secondary ID Type	

    SECONDARY-ID-TYPE 

	TEXT	enum SecondaryIdType (stats)	Secondary ID Type		Required if there is Secondary ID	protocolSection.identificationModule.secondaryIdInfos.type
/Study/ProtocolSection/IdentificationModule/SecondaryIdInfoList/SecondaryIdInfo/SecondaryIdType
domain
SecondaryIdDomain	Secondary ID Description based on ID Type selected	

    SECONDARY-ID-DOMAIN 

	TEXT (max 119 chars)	text ✓ (stats)	Description		If a Secondary ID Type of "Other Grant/Funding Number," "Registry Identifier," or "Other Identifier" is selected, provide the name of the funding organization, clinical trial registry, or organization that issued the identifier.	protocolSection.identificationModule.secondaryIdInfos.domain
/Study/ProtocolSection/IdentificationModule/SecondaryIdInfoList/SecondaryIdInfo/SecondaryIdDomain
link
SecondaryIdLink	Secondary ID Link	

	TEXT	text (stats)		
URL link based on SecondaryId and SecondaryIdType, including system-generated links to NIH RePORTER, specifically (associated with the types of federal funding identified as SecondaryIdType)
		protocolSection.identificationModule.secondaryIdInfos.link
/Study/ProtocolSection/IdentificationModule/SecondaryIdInfoList/SecondaryIdInfo/SecondaryIdLink
numSecondaryIds ✗
NumSecondaryIds	Number of Secondary IDs	

    NUMBER-OF-SECONDARY_IDS 

	FUNC NOccrs(SecondaryId)	short (stats)		
Number of Secondary ID for an NCT
		protocolSection.identificationModule.numSecondaryIds
/Study/ProtocolSection/IdentificationModule/NumSecondaryIds
briefTitle
BriefTitle	Brief Title	

    BRIEF-TITLE 

	TEXT (max 300 chars)	text ✓ (stats)	Brief Title		Required for INT/OBS/EA. Has to be unique in PRS	protocolSection.identificationModule.briefTitle
/Study/ProtocolSection/IdentificationModule/BriefTitle
officialTitle
OfficialTitle	Official Title	

    OFFICIAL-TITLE 

	TEXT (max 600 chars)	text ✓ (stats)	Official Title		Required for INT/OBS if Study Start Date is on or after Final Rule Effective Date (FRED). Optional for EA if EA type is "Individual Patients"	protocolSection.identificationModule.officialTitle
/Study/ProtocolSection/IdentificationModule/OfficialTitle
acronym
Acronym	Acronym	

    ACRONYM 

	TEXT (max 14 chars)	text ✓ (stats)	Acronym			protocolSection.identificationModule.acronym
/Study/ProtocolSection/IdentificationModule/Acronym
organization
Organization		

	STRUCT	Organization				protocolSection.identificationModule.organization
/Study/ProtocolSection/IdentificationModule/Organization
fullName
OrgFullName	Organization Full Name	

    ORGANIZATION-FULLNAME 

	TEXT	text (stats)		
A (registered) organization (typically the Responsible Party) that sponsors the clinical  trial (study)
	Provided as part of User Information or Organization Information in a PRS Account	protocolSection.identificationModule.organization.fullName
/Study/ProtocolSection/IdentificationModule/Organization/OrgFullName
class
OrgClass	Organization type	

    ORGANIZATION-CLASS 

	TEXT	enum AgencyClass (stats)		
Organization type
	Redacted for Withheld studies	protocolSection.identificationModule.organization.class
/Study/ProtocolSection/IdentificationModule/Organization/OrgClass
statusModule
StatusModule	Status Module	

	STRUCT	StatusModule	Study Status			protocolSection.statusModule
/Study/ProtocolSection/StatusModule
statusVerifiedDate
StatusVerifiedDate	Record Verification Date	

    VERIFICATION-DATE 

	DATE	PartialDate (stats)	Record Verification Date		Required for INT/OBS/EA	protocolSection.statusModule.statusVerifiedDate
/Study/ProtocolSection/StatusModule/StatusVerifiedDate
overallStatus
OverallStatus	Overall Recruitment Status or Expanded Access Status	

    OVERALL-STATUS 

	TEXT	enum Status (stats)	Overall Recruitment Status		Required for INT/OBS/EA.
Withheld studies are a special case where a FDA-regulated device product is not yet approved or cleared by U.S. FDA (DelayedPosting). It's a system generated status.
Withheld study has the following text for Recruitment Status
This trial has been identified as being associated with a clinical device that has not been approved or cleared by the US Food and Drug Administration. Under the terms of US Public Law 110-85, Title VIII, Section 801, the details of this study are not available to the public.	protocolSection.statusModule.overallStatus
/Study/ProtocolSection/StatusModule/OverallStatus
lastKnownStatus
LastKnownStatus	Last Known Status	

    LAST-KNOWN-STATUS 

	TEXT	enum Status (stats)		
A study on ClinicalTrials.gov whose last known status was recruiting; not yet recruiting; or active, not recruiting but that has passed its completion date, and the status has not been last verified within the past 2 years.
	The study's current recruitment status is "Unknown Status"	protocolSection.statusModule.lastKnownStatus
/Study/ProtocolSection/StatusModule/LastKnownStatus
delayedPosting
DelayedPosting	Delayed Posting	

    DELAYED 

	BOOLEAN	boolean (stats)	Post Prior to U.S. FDA Approval or Clearance			protocolSection.statusModule.delayedPosting
/Study/ProtocolSection/StatusModule/DelayedPosting
whyStopped
WhyStopped	Reason why a study stopped	

    WHY-STOPPED 

	MARKUP	markup ✓ (stats)	Why Study Stopped			protocolSection.statusModule.whyStopped
/Study/ProtocolSection/StatusModule/WhyStopped
expandedAccessInfo
ExpandedAccessInfo	Expanded Access Info	

	STRUCT	ExpandedAccessInfo		
EA study info related to an INT/OBS study. Is in the Study Status module for an EA record and in the Oversight module for a study that has EA
		protocolSection.statusModule.expandedAccessInfo
/Study/ProtocolSection/StatusModule/ExpandedAccessInfo
hasExpandedAccess
HasExpandedAccess	Has EA for compassionate use	

    HAS-EXPANDED-ACCESS 

	TEXT	boolean (stats)	Availability of Expanded Access			protocolSection.statusModule.expandedAccessInfo.hasExpandedAccess
/Study/ProtocolSection/StatusModule/ExpandedAccessInfo/HasExpandedAccess
nctId
ExpandedAccessNCTId	NCT of an EA study	

    HAS-EXPANDED-ACCESS-NCT 

	TEXT	text ✓ (stats)	Expanded Access Record NCT Number			protocolSection.statusModule.expandedAccessInfo.nctId
/Study/ProtocolSection/StatusModule/ExpandedAccessInfo/ExpandedAccessNCTId
statusForNctId
ExpandedAccessStatusForNCTId	EA Recruitment Status	

	TEXT	enum ExpandedAccessStatus (stats)		
recruitment status of the EA study that's linked to INT/OBS
		protocolSection.statusModule.expandedAccessInfo.statusForNctId
/Study/ProtocolSection/StatusModule/ExpandedAccessInfo/ExpandedAccessStatusForNCTId
startDateStruct
StartDateStruct		

	STRUCT	PartialDateStruct				protocolSection.statusModule.startDateStruct
/Study/ProtocolSection/StatusModule/StartDateStruct
date
StartDate	Study Start Date	

    START-DATE 

	DATE	PartialDate (stats)	Study Start Date		Required for INT/OBS that are FRED. Day is not required in PRS when date type is Anticipated. Redacted for Withheld studies
Some EA studies have Study Start Date even though it's not mentioned in the DED and there are no FE input fields in PRS for EA (possibly switched from INT to EA at some point).	protocolSection.statusModule.startDateStruct.date
/Study/ProtocolSection/StatusModule/StartDateStruct/StartDate
type
StartDateType	Study Start Date Type	

	TEXT	enum DateType (stats)		
Date Type
	Required for INT/OBS that are FRED. Redacted for Withheld studies	protocolSection.statusModule.startDateStruct.type
/Study/ProtocolSection/StatusModule/StartDateStruct/StartDateType
primaryCompletionDateStruct
PrimaryCompletionDateStruct		

	STRUCT	PartialDateStruct				protocolSection.statusModule.primaryCompletionDateStruct
/Study/ProtocolSection/StatusModule/PrimaryCompletionDateStruct
date
PrimaryCompletionDate	Primary Completion Date	

    COMPLETION-DATE-PRIMARY 

	DATE	PartialDate (stats)	Primary Completion Date		Required for INT/OBS. However, some older INT and OBS studies do not have Primary Completion Date.
Some EA studies has Primary Completion Date/Type even though they are not in DED and there are no FE input fields in PRS
Day is not required if Date Type is Anticipated	protocolSection.statusModule.primaryCompletionDateStruct.date
/Study/ProtocolSection/StatusModule/PrimaryCompletionDateStruct/PrimaryCompletionDate
type
PrimaryCompletionDateType	Primary Completion Date Type	

	TEXT	enum DateType (stats)	Primary Completion Date			protocolSection.statusModule.primaryCompletionDateStruct.type
/Study/ProtocolSection/StatusModule/PrimaryCompletionDateStruct/PrimaryCompletionDateType
completionDateStruct
CompletionDateStruct		

	STRUCT	PartialDateStruct				protocolSection.statusModule.completionDateStruct
/Study/ProtocolSection/StatusModule/CompletionDateStruct
date
CompletionDate	Study Completion Date	

    COMPLETION-DATE 

	DATE	PartialDate (stats)	Study Completion Date		Required for INT/OBS that are FRED	protocolSection.statusModule.completionDateStruct.date
/Study/ProtocolSection/StatusModule/CompletionDateStruct/CompletionDate
type
CompletionDateType	Study Completion Date Type	

	TEXT	enum DateType (stats)	Study Completion Date			protocolSection.statusModule.completionDateStruct.type
/Study/ProtocolSection/StatusModule/CompletionDateStruct/CompletionDateType
studyFirstSubmitDate
StudyFirstSubmitDate	Study First Submitted Date	

    STUDY-FIRST-SUBMITTED
    FIRST-RECEIVED-DATE 

	DATE	NormalizedDate (stats)		
The date on which the study sponsor or investigator first submitted a study record to ClinicalTrials.gov. There is typically a delay of a few days between the first submitted date and the record's availability on ClinicalTrials.gov (the first posted date).
	System generated	protocolSection.statusModule.studyFirstSubmitDate
/Study/ProtocolSection/StatusModule/StudyFirstSubmitDate
studyFirstSubmitYear ✗
StudyFirstSubmitYear	Study First Submitted Year	

	FUNC MinYear(StudyFirstSubmitDate)	short (stats)			System generated	protocolSection.statusModule.studyFirstSubmitYear
/Study/ProtocolSection/StatusModule/StudyFirstSubmitYear
studyFirstSubmitQcDate
StudyFirstSubmitQCDate	Study First Submission Date that Met QC Criteria	

    STUDY-FIRST-SUBMITTED-QC 

	DATE	NormalizedDate (stats)		
The date on which the study sponsor or investigator first submits a study record that is consistent with National Library of Medicine (NLM) quality control (QC) review criteria. The sponsor or investigator may need to revise and submit a study record one or more times before NLM's QC review criteria are met. It is the responsibility of the sponsor or investigator to ensure that the study record is consistent with the NLM QC review criteria.
	System generated	protocolSection.statusModule.studyFirstSubmitQcDate
/Study/ProtocolSection/StatusModule/StudyFirstSubmitQCDate
studyFirstPostDateStruct
StudyFirstPostDateStruct		

	STRUCT	DateStruct				protocolSection.statusModule.studyFirstPostDateStruct
/Study/ProtocolSection/StatusModule/StudyFirstPostDateStruct
date
StudyFirstPostDate	Study First Posted Date	

    STUDY-FIRST-POSTED 

	DATE	NormalizedDate (stats)		
The date on which the study record was first available on ClinicalTrials.gov after National Library of Medicine (NLM) quality control (QC) review has concluded. There is typically a delay of a few days between the date the study sponsor or investigator submitted the study record and the first posted date.
	System generated	protocolSection.statusModule.studyFirstPostDateStruct.date
/Study/ProtocolSection/StatusModule/StudyFirstPostDateStruct/StudyFirstPostDate
studyFirstPostYear ✗
StudyFirstPostYear	Year the Study First Posted on the Public Site	

	FUNC MinYear(StudyFirstPostDate)	short (stats)			System generated	protocolSection.statusModule.studyFirstPostDateStruct.studyFirstPostYear
/Study/ProtocolSection/StatusModule/StudyFirstPostDateStruct/StudyFirstPostYear
type
StudyFirstPostDateType	First Study Posted Date Type	

	TEXT	enum DateType (stats)			System generated	protocolSection.statusModule.studyFirstPostDateStruct.type
/Study/ProtocolSection/StatusModule/StudyFirstPostDateStruct/StudyFirstPostDateType
resultsWaived
ResultsWaived		

	BOOLEAN	boolean (stats)				protocolSection.statusModule.resultsWaived
/Study/ProtocolSection/StatusModule/ResultsWaived
resultsFirstSubmitDate
ResultsFirstSubmitDate	Results First Submitted Date	

    RESULTS-FIRST-SUBMITTED
    FIRST-RECEIVED-RESULTS-DATE 

	DATE	NormalizedDate (stats)		
The date on which the study sponsor or investigator first submits a study record with summary results information. There is typically a delay between the results first submitted date and when summary results information becomes available on ClinicalTrials.gov (the results first posted date).
	System generated	protocolSection.statusModule.resultsFirstSubmitDate
/Study/ProtocolSection/StatusModule/ResultsFirstSubmitDate
resultsFirstSubmitYear ✗
ResultsFirstSubmitYear	Results First Submitted Year	

	FUNC MinYear(ResultsFirstSubmitDate)	short (stats)			System generated	protocolSection.statusModule.resultsFirstSubmitYear
/Study/ProtocolSection/StatusModule/ResultsFirstSubmitYear
resultsFirstSubmitQcDate
ResultsFirstSubmitQCDate	Results First Submitted that Met QC Criteria	

    RESULTS-FIRST-SUBMITTED-QC 

	DATE	NormalizedDate (stats)		
The date on which the study sponsor or investigator first submits a study record with summary results information that is consistent with National Library of Medicine (NLM) quality control (QC) review criteria. The sponsor or investigator may need to revise and submit results information one or more times before NLM's QC review criteria are met. It is the responsibility of the sponsor or investigator to ensure that the study record is consistent with the NLM QC review criteria.
	System generated	protocolSection.statusModule.resultsFirstSubmitQcDate
/Study/ProtocolSection/StatusModule/ResultsFirstSubmitQCDate
resultsFirstPostDateStruct
ResultsFirstPostDateStruct		

	STRUCT	DateStruct				protocolSection.statusModule.resultsFirstPostDateStruct
/Study/ProtocolSection/StatusModule/ResultsFirstPostDateStruct
date
ResultsFirstPostDate	Results First Posted Date	

    RESULTS-FIRST-POSTED 

	DATE	NormalizedDate (stats)		
The date on which summary results information was first available on ClinicalTrials.gov after National Library of Medicine (NLM) quality control (QC) review has concluded. There is typically a delay between the date the study sponsor or investigator first submits summary results information (the results first submitted date) and the results first posted date. Some results information may be available at an earlier date if Results First Posted with QC Comments.
	System generated	protocolSection.statusModule.resultsFirstPostDateStruct.date
/Study/ProtocolSection/StatusModule/ResultsFirstPostDateStruct/ResultsFirstPostDate
resultsFirstPostYear ✗
ResultsFirstPostYear	Results First Posted Year	

	FUNC MinYear(ResultsFirstPostDate)	short (stats)			System generated	protocolSection.statusModule.resultsFirstPostDateStruct.resultsFirstPostYear
/Study/ProtocolSection/StatusModule/ResultsFirstPostDateStruct/ResultsFirstPostYear
type
ResultsFirstPostDateType	Results First Posted Date Type	

	TEXT	enum DateType (stats)		
Results first posted date type. Due to historical reason, some studies have date type "Estimate"
	System generated	protocolSection.statusModule.resultsFirstPostDateStruct.type
/Study/ProtocolSection/StatusModule/ResultsFirstPostDateStruct/ResultsFirstPostDateType
dispFirstSubmitDate
DispFirstSubmitDate	Certification/Extension (aka Delayed Results) First Submitted Date	

    DISPOSITION-FIRST-SUBMITTED
    FIRST-RECEIVED-DISPOSITION-DATE 

	DATE	NormalizedDate (stats)		
The date on which the study sponsor or investigator first submitted a certification or an extension request to delay submission of results. A sponsor or investigator who submits a certification can delay results submission up to 2 years after this date, unless certain events occur sooner. There is typically a delay between the date the certification or extension request was submitted and the date the information is first available on ClinicalTrials.gov (certification/extension first posted).
	System generated	protocolSection.statusModule.dispFirstSubmitDate
/Study/ProtocolSection/StatusModule/DispFirstSubmitDate
dispFirstSubmitYear ✗
DispFirstSubmitYear	Certification/Extension First Submitted Year	

	FUNC MinYear(DispFirstSubmitDate)	short (stats)			System generated	protocolSection.statusModule.dispFirstSubmitYear
/Study/ProtocolSection/StatusModule/DispFirstSubmitYear
dispFirstSubmitQcDate
DispFirstSubmitQCDate	Certification/Extension First Submitted that Passed QC Review	

    DISPOSITION-FIRST-SUBMITTED-QC 

	DATE	NormalizedDate (stats)		
Certification/extension first submitted that met QC criteria
	System generated	protocolSection.statusModule.dispFirstSubmitQcDate
/Study/ProtocolSection/StatusModule/DispFirstSubmitQCDate
dispFirstPostDateStruct
DispFirstPostDateStruct		

	STRUCT	DateStruct				protocolSection.statusModule.dispFirstPostDateStruct
/Study/ProtocolSection/StatusModule/DispFirstPostDateStruct
date
DispFirstPostDate	Certification/Extension First Posted Date	

    DISPOSITION-FIRST-POSTED 

	DATE	NormalizedDate (stats)		
The date on which information about a certification to delay submission of results or an extension request was first available on ClinicalTrials.gov. ClinicalTrials.gov does not indicate whether the submission was a certification or extension request. There is typically a delay between the date the study sponsor or investigator submitted the certification or extension request and the first posted date.
	System generated	protocolSection.statusModule.dispFirstPostDateStruct.date
/Study/ProtocolSection/StatusModule/DispFirstPostDateStruct/DispFirstPostDate
dispFirstPostYear ✗
DispFirstPostYear	Certification/Extension First Posted Year	

	FUNC MinYear(DispFirstPostDate)	short (stats)			System generated	protocolSection.statusModule.dispFirstPostDateStruct.dispFirstPostYear
/Study/ProtocolSection/StatusModule/DispFirstPostDateStruct/DispFirstPostYear
type
DispFirstPostDateType	Certification/Extension First Posted Date Type	

	TEXT	enum DateType (stats)		
Due to data requirements and collections over the years, some studies might have "Estimate" dates
	System generated	protocolSection.statusModule.dispFirstPostDateStruct.type
/Study/ProtocolSection/StatusModule/DispFirstPostDateStruct/DispFirstPostDateType
lastUpdateSubmitDate
LastUpdateSubmitDate	Last Update Submitted Date	

    LAST-UPDATE-SUBMITTED
    LAST-RELEASE-DATE 

	DATE	NormalizedDate (stats)		
The most recent date on which the study sponsor or investigator submitted changes to a study record to ClinicalTrials.gov. There is typically a delay of a few days between the last update submitted date and when the date changes are posted on ClinicalTrials.gov (the last update posted date).
	System generated	protocolSection.statusModule.lastUpdateSubmitDate
/Study/ProtocolSection/StatusModule/LastUpdateSubmitDate
lastUpdateSubmitYear ✗
LastUpdateSubmitYear	Last Update Submitted Year	

	FUNC MinYear(LastUpdateSubmitDate)	short (stats)			System generated	protocolSection.statusModule.lastUpdateSubmitYear
/Study/ProtocolSection/StatusModule/LastUpdateSubmitYear
lastUpdatePostDateStruct
LastUpdatePostDateStruct		

	STRUCT	DateStruct		
Data structure for last update posted
		protocolSection.statusModule.lastUpdatePostDateStruct
/Study/ProtocolSection/StatusModule/LastUpdatePostDateStruct
date
LastUpdatePostDate	Last Update Posted Date	

    LAST-UPDATE-POSTED 

	DATE	NormalizedDate (stats)		
The most recent date on which changes to a study record were made available on ClinicalTrials.gov. There may be a delay between when the changes were submitted to ClinicalTrials.gov by the study's sponsor or investigator (the last update submitted date) and the last update posted date.
	System generated	protocolSection.statusModule.lastUpdatePostDateStruct.date
/Study/ProtocolSection/StatusModule/LastUpdatePostDateStruct/LastUpdatePostDate
lastUpdatePostYear ✗
LastUpdatePostYear	Last Update Posted Year	

	FUNC MinYear(LastUpdatePostDate)	short (stats)		
Study record last update posted year on public site
	System generated	protocolSection.statusModule.lastUpdatePostDateStruct.lastUpdatePostYear
/Study/ProtocolSection/StatusModule/LastUpdatePostDateStruct/LastUpdatePostYear
type
LastUpdatePostDateType	Last Update Posted Date Type	

	TEXT	enum DateType (stats)		
Due to data requirements and collections over the years, some studies might have "Estimate" dates
	System generated	protocolSection.statusModule.lastUpdatePostDateStruct.type
/Study/ProtocolSection/StatusModule/LastUpdatePostDateStruct/LastUpdatePostDateType
sponsorCollaboratorsModule
SponsorCollaboratorsModule		

	STRUCT	SponsorCollaboratorsModule	Sponsor/Collaborators			protocolSection.sponsorCollaboratorsModule
/Study/ProtocolSection/SponsorCollaboratorsModule
responsibleParty
ResponsibleParty	Responsible Party	

	STRUCT	ResponsibleParty	Investigator Information			protocolSection.sponsorCollaboratorsModule.responsibleParty
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty
type
ResponsiblePartyType	Responsible Party Type	

    RESPONSIBLE-PARTY-TYPE 

	TEXT	enum ResponsiblePartyType (stats)	Responsible Party, by Official Title		Required for INT/OBS/EA for FRED.
Redacted for Withheld studies	protocolSection.sponsorCollaboratorsModule.responsibleParty.type
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty/ResponsiblePartyType
investigatorFullName
ResponsiblePartyInvestigatorFullName	Responsible Party Investigator Full Name	

	TEXT	text ✓ (stats)	Investigator Name		Required if the Responsible Party is either "Principal Investigator" or "Sponsor-Investigator".
Select from PRS user accounts associated with the organization
The Investigator Name (i.e., the Full Name from the PRS account record) must be a person's full name for display on ClinicalTrials.gov.
Redacted for Withheld studies	protocolSection.sponsorCollaboratorsModule.responsibleParty.investigatorFullName
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty/ResponsiblePartyInvestigatorFullName
investigatorTitle
ResponsiblePartyInvestigatorTitle	Responsible Party Investigator Title	

	TEXT (max 254 chars)	text ✓ (stats)	Investigator Official Title		Required if the Responsible Party is either "Principal Investigator" or "Sponsor-Investigator".
Redacted for Withheld studies	protocolSection.sponsorCollaboratorsModule.responsibleParty.investigatorTitle
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty/ResponsiblePartyInvestigatorTitle
investigatorAffiliation
ResponsiblePartyInvestigatorAffiliation	Responsible Party Investigator Affiliation	

	TEXT (max 160 chars)	text ✓ (stats)	Investigator Affiliation		Required if the Responsible Party is either "Principal Investigator" or "Sponsor-Investigator".
Redacted for Withheld studies	protocolSection.sponsorCollaboratorsModule.responsibleParty.investigatorAffiliation
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty/ResponsiblePartyInvestigatorAffiliation
oldNameTitle
ResponsiblePartyOldNameTitle	Older format for Responsible Party Investigator Title	

	TEXT	text ✓ (stats)	Investigator Name		[Redacted] for Withheld studies	protocolSection.sponsorCollaboratorsModule.responsibleParty.oldNameTitle
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty/ResponsiblePartyOldNameTitle
oldOrganization
ResponsiblePartyOldOrganization	Older format for Responsible Party Investigator Organization	

	TEXT	text ✓ (stats)	Investigator Affiliation		[Redacted] for Withheld studies	protocolSection.sponsorCollaboratorsModule.responsibleParty.oldOrganization
/Study/ProtocolSection/SponsorCollaboratorsModule/ResponsibleParty/ResponsiblePartyOldOrganization
leadSponsor
LeadSponsor	Lead Sponsor	

	STRUCT	Sponsor		
The organization or person who initiates the study and who has authority and control over the study.
	Required for INT/Obs/EA
[Redacted] for Withheld studies	protocolSection.sponsorCollaboratorsModule.leadSponsor
/Study/ProtocolSection/SponsorCollaboratorsModule/LeadSponsor
name
LeadSponsorName	Lead Sponsor Name	

    SPONSOR
    LEAD-SPONSOR 

	TEXT (max 160 chars)	text (stats)	Name of the Sponsor		[Redacted] for Withheld studies	protocolSection.sponsorCollaboratorsModule.leadSponsor.name
/Study/ProtocolSection/SponsorCollaboratorsModule/LeadSponsor/LeadSponsorName
class
LeadSponsorClass	Leas Sponsor Type	

    LEAD-SPONSOR-CLASS 

	TEXT	enum AgencyClass (stats)		
Sponsor organization type: Type of lead sponsor
	Required. Stored in PRS Agency Service(?) [Redacted] for Withheld studies	protocolSection.sponsorCollaboratorsModule.leadSponsor.class
/Study/ProtocolSection/SponsorCollaboratorsModule/LeadSponsor/LeadSponsorClass
collaborators
Collaborator	Collaborator	

	STRUCT	Sponsor[]		
Other organizations, if any, providing support. Support may include funding, design, implementation, data analysis or reporting.  The responsible party is responsible for confirming all collaborators before listing them
		protocolSection.sponsorCollaboratorsModule.collaborators
/Study/ProtocolSection/SponsorCollaboratorsModule/CollaboratorList/Collaborator
name
CollaboratorName	Collaborator Name	

    COLLABORATOR 

	TEXT (max 160 chars)	text (stats)	Collaborators			protocolSection.sponsorCollaboratorsModule.collaborators.name
/Study/ProtocolSection/SponsorCollaboratorsModule/CollaboratorList/Collaborator/CollaboratorName
class
CollaboratorClass	Collaborator Type	

    COLLABORATOR-CLASS 

	TEXT	enum AgencyClass (stats)		
Type of collaborator
	Redacted for Withheld studies	protocolSection.sponsorCollaboratorsModule.collaborators.class
/Study/ProtocolSection/SponsorCollaboratorsModule/CollaboratorList/Collaborator/CollaboratorClass
numCollaborators ✗
NumCollaborators	Number of Collaborators for a study	

	FUNC NOccrs(Collaborator)	short (stats)		
number of collaborators
	internal	protocolSection.sponsorCollaboratorsModule.numCollaborators
/Study/ProtocolSection/SponsorCollaboratorsModule/NumCollaborators
numCollaboratorsPlusLead ✗
NumCollaboratorsPlusLead	Number of Collaborators Plus Lead Sponsor	

	FUNC NOccrs(LeadSponsor, Collaborator)	short (stats)				protocolSection.sponsorCollaboratorsModule.numCollaboratorsPlusLead
/Study/ProtocolSection/SponsorCollaboratorsModule/NumCollaboratorsPlusLead
oversightModule
OversightModule	Oversight Module	

	STRUCT	OversightModule	Oversight			protocolSection.oversightModule
/Study/ProtocolSection/OversightModule
oversightHasDmc
OversightHasDMC	Has Data Monitoring Committee (DMC)	

    HAS-DMC 

	BOOLEAN	boolean (stats)	Data Monitoring Committee		Marked as [Missing] for Withheld studies	protocolSection.oversightModule.oversightHasDmc
/Study/ProtocolSection/OversightModule/OversightHasDMC
isFdaRegulatedDrug
IsFDARegulatedDrug	Is FDA Regulated Drug	

	BOOLEAN	boolean (stats)	Studies a U.S. FDA-regulated Drug Product		Required for INT/FRED. Marked as [Missing] for Withheld studies	protocolSection.oversightModule.isFdaRegulatedDrug
/Study/ProtocolSection/OversightModule/IsFDARegulatedDrug
isFdaRegulatedDevice
IsFDARegulatedDevice	Is FDA Regulated Device	

	BOOLEAN	boolean (stats)	Studies a U.S. FDA-regulated Device Product		Required for INT/FRED. Marked as [Missing] for Withheld studies	protocolSection.oversightModule.isFdaRegulatedDevice
/Study/ProtocolSection/OversightModule/IsFDARegulatedDevice
isUnapprovedDevice
IsUnapprovedDevice	Is Unapproved Device	

	BOOLEAN	boolean (stats)	Device Product Not Approved or Cleared by U.S. FDA		Required for INT/FRED. Marked as [Missing] for Withheld studies	protocolSection.oversightModule.isUnapprovedDevice
/Study/ProtocolSection/OversightModule/IsUnapprovedDevice
isPpsd
IsPPSD	Pediatric Postmarket Surveillance of a Device Product	

	BOOLEAN	boolean (stats)	Pediatric Postmarket Surveillance of a Device Product		Required only if this study is a pediatric postmarket surveillance of a device product ordered by the U.S. FDA.
Marked as [Missing] for Withheld studies	protocolSection.oversightModule.isPpsd
/Study/ProtocolSection/OversightModule/IsPPSD
isUsExport
IsUSExport	Product Exported from US	

	BOOLEAN	boolean (stats)	Product Manufactured in and Exported from the U.S.		Required if U.S. FDA-regulated Drug and/or U.S. FDA-regulated Device is "Yes," U.S. FDA IND or IDE is "No", and Facility Information does not include at least one U.S. location.
Marked as [Missing] for Withheld studies	protocolSection.oversightModule.isUsExport
/Study/ProtocolSection/OversightModule/IsUSExport
fdaaa801Violation
FDAAA801Violation		

	BOOLEAN	boolean (stats)				protocolSection.oversightModule.fdaaa801Violation
/Study/ProtocolSection/OversightModule/FDAAA801Violation
descriptionModule
DescriptionModule	Description Module	

	STRUCT	DescriptionModule	Study Description			protocolSection.descriptionModule
/Study/ProtocolSection/DescriptionModule
briefSummary
BriefSummary	Brief Summary	

	MARKUP (max 5000 chars)	markup ✓ (stats)	Brief Summary		Required for INT/OBS/EA. Redacted for Withheld studies	protocolSection.descriptionModule.briefSummary
/Study/ProtocolSection/DescriptionModule/BriefSummary
detailedDescription
DetailedDescription	Detailed Description	

	MARKUP (max 32000 chars)	markup ✓ (stats)	Detailed Description			protocolSection.descriptionModule.detailedDescription
/Study/ProtocolSection/DescriptionModule/DetailedDescription
conditionsModule
ConditionsModule	Conditions Module	

	STRUCT	ConditionsModule	Conditions and Keywords			protocolSection.conditionsModule
/Study/ProtocolSection/ConditionsModule
conditions
Condition	Condition/Disease	

    CONDITIONS 

	TEXT (max 160 chars)	text[] ✓ (stats)	Primary Disease or Condition Being Studied in the Trial, or the Focus of the Study		Required for INT/OBS. Redacted for Withheld studies. Optional for EA if EA type is "individual patient"	protocolSection.conditionsModule.conditions
/Study/ProtocolSection/ConditionsModule/ConditionList/Condition
numConditions ✗
NumConditions	Number of Conditioned Entered by DB for a Study	

    NUMBER-OF-CONDITIONS 

	FUNC NOccrs(Condition)	short (stats)			Internally calculated	protocolSection.conditionsModule.numConditions
/Study/ProtocolSection/ConditionsModule/NumConditions
keywords
Keyword	Keyword	

    KEYWORDS 

	TEXT	text[] ✓ (stats)	Keywords			protocolSection.conditionsModule.keywords
/Study/ProtocolSection/ConditionsModule/KeywordList/Keyword
numKeywords ✗
NumKeywords	Number of Keywords for a Study	

    NUMBER-OF-KEYWORDS 

	FUNC NOccrs(Keyword)	short (stats)			Internally calculated	protocolSection.conditionsModule.numKeywords
/Study/ProtocolSection/ConditionsModule/NumKeywords
designModule
DesignModule	Design Module	

	STRUCT	DesignModule	Study Design			protocolSection.designModule
/Study/ProtocolSection/DesignModule
studyType
StudyType	Study Type	

    STUDY-TYPE
    STUDY-TYPES 

	TEXT	enum StudyType (stats)	Study Type		Required. Marked as [Missing] for Withheld studies	protocolSection.designModule.studyType
/Study/ProtocolSection/DesignModule/StudyType
nPtrsToThisExpAccNctId
NPtrsToThisExpAccNCTId	Number of References to an Expanded Access Study	

	NUMERIC	number (stats)		
Number of studies that reference this EA study
	Internally calculated	protocolSection.designModule.nPtrsToThisExpAccNctId
/Study/ProtocolSection/DesignModule/NPtrsToThisExpAccNCTId
expandedAccessTypes
ExpandedAccessTypes	Expanded Access Type	

	STRUCT	ExpandedAccessTypes		
The type(s) of expanded access for which the investigational drug product (including a biological product) is available, as specified in U.S. FDA regulations
	Required for EA	protocolSection.designModule.expandedAccessTypes
/Study/ProtocolSection/DesignModule/ExpandedAccessTypes
individual
ExpAccTypeIndividual	Individual Patients	

	BOOLEAN	boolean (stats)		
For individual participants, including for emergency use, as specified in 21 CFR 312.310. Allows a single patient, with a serious disease or condition who cannot participate in a clinical trial, access to a drug or biological product that has not been approved by the FDA. This category also includes access in an emergency situation.
		protocolSection.designModule.expandedAccessTypes.individual
/Study/ProtocolSection/DesignModule/ExpandedAccessTypes/ExpAccTypeIndividual
intermediate
ExpAccTypeIntermediate	Intermediate-type Population	

	BOOLEAN	boolean (stats)		
For intermediate-size participant populations, as specified in 21 CFR 312.315. Allows more than one patient (but generally fewer patients than through a Treatment IND/Protocol) access to a drug or biological product that has not been approved by the FDA. This type of expanded access is used when multiple patients with the same disease or condition seek access to a specific drug or biological product that has not been approved by the FDA.
		protocolSection.designModule.expandedAccessTypes.intermediate
/Study/ProtocolSection/DesignModule/ExpandedAccessTypes/ExpAccTypeIntermediate
treatment
ExpAccTypeTreatment	Treatment IND/Protocol	

	BOOLEAN	boolean (stats)		
Under a treatment IND or treatment protocol, as specified in 21 CFR 312.320. Allows a large, widespread population access to a drug or biological product that has not been approved by the FDA. This type of expanded access can only be provided if the product is already being developed for marketing for the same use as the expanded access use.
		protocolSection.designModule.expandedAccessTypes.treatment
/Study/ProtocolSection/DesignModule/ExpandedAccessTypes/ExpAccTypeTreatment
patientRegistry
PatientRegistry	Patient Registry	

    PATIENT-REGISTRY 

	BOOLEAN	boolean (stats)		
A type of observational study that collects information about patients' medical conditions and/or treatments to better understand how a condition or treatment affects patients in the real world.
		protocolSection.designModule.patientRegistry
/Study/ProtocolSection/DesignModule/PatientRegistry
targetDuration
TargetDuration	Target Follow-Up Duration	

	TIME	NormalizedTime (stats)	Target Follow-Up Duration		Required for OBS that are Patient Registry	protocolSection.designModule.targetDuration
/Study/ProtocolSection/DesignModule/TargetDuration
phases
Phase	Study Phase	

    PHASE 

	TEXT	enum Phase[] (stats)	Study Phase		For INT studies of a drug or biologic product. Can have Missing or N/A in addition to Phases. If Missing, nothing is displayed on public site. If N/A, public site displays "Not Applicable"
N/A is recommended for trials that do not involve drug or biologic products.
Pulldown menu in PRS can have combo of 2 phases, 1&2, or 2&3.	protocolSection.designModule.phases
/Study/ProtocolSection/DesignModule/PhaseList/Phase
numPhases ✗
NumPhases	Number of Phases	

	FUNC NOccrs(Phase)	short (stats)		
Indicate which phase(s) the study is in
	Internally calculated	protocolSection.designModule.numPhases
/Study/ProtocolSection/DesignModule/NumPhases
designInfo
DesignInfo	Design Info	

	STRUCT	DesignInfo	Interventional Study Design			protocolSection.designModule.designInfo
/Study/ProtocolSection/DesignModule/DesignInfo
allocation
DesignAllocation	Design Allocation	

    DESIGN-ALLOCATION 

	TEXT	enum DesignAllocation (stats)	Allocation		Required for FRED (INT). Marked as [Missing] for Withheld studies	protocolSection.designModule.designInfo.allocation
/Study/ProtocolSection/DesignModule/DesignInfo/DesignAllocation
interventionModel
DesignInterventionModel	Interventional Study Design	

    DESIGN-INTERVENTION-MODEL 

	TEXT	enum InterventionalAssignment (stats)	Interventional Study Model		Required for FRED (INT). Marked as [Missing] for Withheld studies	protocolSection.designModule.designInfo.interventionModel
/Study/ProtocolSection/DesignModule/DesignInfo/DesignInterventionModel
interventionModelDescription
DesignInterventionModelDescription	Interventional Study Design Description	

    DESIGN-INTERVENTION-MODEL-DESCRIPTION 

	MARKUP	markup ✓ (stats)	Model Description			protocolSection.designModule.designInfo.interventionModelDescription
/Study/ProtocolSection/DesignModule/DesignInfo/DesignInterventionModelDescription
primaryPurpose
DesignPrimaryPurpose	Design Primary Purpose	

    DESIGN-PRIMARY-PURPOSE 

	TEXT	enum PrimaryPurpose (stats)	Primary Purpose		Required for FRED (INT). Marked as [Missing] for Withheld studies	protocolSection.designModule.designInfo.primaryPurpose
/Study/ProtocolSection/DesignModule/DesignInfo/DesignPrimaryPurpose
observationalModel
DesignObservationalModel	Observational Study Model	

    DESIGN-OBSERVATIONAL-MODEL 

	TEXT	enum ObservationalModel (stats)	Observational Study Model		Required for OBS but [Missing] is allowed	protocolSection.designModule.designInfo.observationalModel
/Study/ProtocolSection/DesignModule/DesignInfo/DesignObservationalModel
timePerspective
DesignTimePerspective	Time Perspective	

    DESIGN-TIME-PERSPECTIVE 

	TEXT	enum DesignTimePerspective (stats)	Time Perspective		Required for OBS but [Missing] is allowed	protocolSection.designModule.designInfo.timePerspective
/Study/ProtocolSection/DesignModule/DesignInfo/DesignTimePerspective
maskingInfo
DesignMaskingInfo		

	STRUCT	MaskingBlock				protocolSection.designModule.designInfo.maskingInfo
/Study/ProtocolSection/DesignModule/DesignInfo/DesignMaskingInfo
masking
DesignMasking	Design Masking	

    DESIGN-MASKING 

	TEXT	enum DesignMasking (stats)	Masking			protocolSection.designModule.designInfo.maskingInfo.masking
/Study/ProtocolSection/DesignModule/DesignInfo/DesignMaskingInfo/DesignMasking
maskingDescription
DesignMaskingDescription	Masking Description	

	MARKUP (max 1000 chars)	markup ✓ (stats)	Masking Description			protocolSection.designModule.designInfo.maskingInfo.maskingDescription
/Study/ProtocolSection/DesignModule/DesignInfo/DesignMaskingInfo/DesignMaskingDescription
whoMasked
DesignWhoMasked		

    DESIGN-WHO-MASKED 

	TEXT	enum WhoMasked[] (stats)	Masking		Required for FRED (INT). Marked as [Missing] for Withheld studies	protocolSection.designModule.designInfo.maskingInfo.whoMasked
/Study/ProtocolSection/DesignModule/DesignInfo/DesignMaskingInfo/DesignWhoMaskedList/DesignWhoMasked
numDesignWhoMaskeds ✗
NumDesignWhoMaskeds		

	FUNC NOccrs(DesignWhoMasked)	short (stats)				protocolSection.designModule.designInfo.maskingInfo.numDesignWhoMaskeds
/Study/ProtocolSection/DesignModule/DesignInfo/DesignMaskingInfo/NumDesignWhoMaskeds
bioSpec
BioSpec		

	STRUCT	BioSpec				protocolSection.designModule.bioSpec
/Study/ProtocolSection/DesignModule/BioSpec
retention
BioSpecRetention	Biospecimen Retention	

    BIOSPECIMEN-RETENTION 

	TEXT	enum BioSpecRetention (stats)	Biospecimen Retention			protocolSection.designModule.bioSpec.retention
/Study/ProtocolSection/DesignModule/BioSpec/BioSpecRetention
description
BioSpecDescription	Biospecimen Description	

    BIOSPECIMEN-DESCRIPTION 

	MARKUP (max 1000 chars)	markup ✓ (stats)	Biospecimen Description			protocolSection.designModule.bioSpec.description
/Study/ProtocolSection/DesignModule/BioSpec/BioSpecDescription
enrollmentInfo
EnrollmentInfo		

	STRUCT	EnrollmentInfo			Required for OBS but [Missing] is allowed	protocolSection.designModule.enrollmentInfo
/Study/ProtocolSection/DesignModule/EnrollmentInfo
count
EnrollmentCount	Enrollment	

    ENROLLMENT 

	NUMERIC	integer (stats)	Enrollment			protocolSection.designModule.enrollmentInfo.count
/Study/ProtocolSection/DesignModule/EnrollmentInfo/EnrollmentCount
type
EnrollmentType	Enrollment Type	

	TEXT	enum EnrollmentType (stats)	Enrollment			protocolSection.designModule.enrollmentInfo.type
/Study/ProtocolSection/DesignModule/EnrollmentInfo/EnrollmentType
armsInterventionsModule
ArmsInterventionsModule		

	STRUCT	ArmsInterventionsModule	Arms, Groups, and Interventions			protocolSection.armsInterventionsModule
/Study/ProtocolSection/ArmsInterventionsModule
armGroups
ArmGroup	Arm Information	

	STRUCT	ArmGroup[]	Arm Information		Required for INT. Can be empty/missing but there has to be an Intervention	protocolSection.armsInterventionsModule.armGroups
/Study/ProtocolSection/ArmsInterventionsModule/ArmGroupList/ArmGroup
label
ArmGroupLabel	Arm Group Label	

    ARM-GROUP-LABEL 

	TEXT (max 100 chars)	text ✓ (stats)	Arm Title		Required for INT. Redacted for Withheld studies
Required for OBS. Can be empty/missing if there only a single group	protocolSection.armsInterventionsModule.armGroups.label
/Study/ProtocolSection/ArmsInterventionsModule/ArmGroupList/ArmGroup/ArmGroupLabel
type
ArmGroupType	Arm Group Type	

    ARM-GROUP-TYPE
    ARM-GROUP-TYPES 

	TEXT	enum ArmGroupType (stats)	Arm Type		Required for INT. Redacted for Withheld studies	protocolSection.armsInterventionsModule.armGroups.type
/Study/ProtocolSection/ArmsInterventionsModule/ArmGroupList/ArmGroup/ArmGroupType
description
ArmGroupDescription	Arm Description for INT, Group/Cohort Description for OBS	

    ARM-GROUP-DESCRIPTION 

	MARKUP (max 999 chars)	markup ✓ (stats)	Arm Description		Required for FRED. Optional for EA if EA type is "Individual Patients"	protocolSection.armsInterventionsModule.armGroups.description
/Study/ProtocolSection/ArmsInterventionsModule/ArmGroupList/ArmGroup/ArmGroupDescription
interventionNames
ArmGroupInterventionName	Arm/Group that Receives a Specific Intervention/Treatment	

    ARM-GROUP-INTERVENTION 

	TEXT	text[] ✓ (stats)	Arm/Group Intervention Name(s)		If multiple Arms or Groups have been specified, indicate which Interventions (or exposures) are in each Arm or Group of the study	protocolSection.armsInterventionsModule.armGroups.interventionNames
/Study/ProtocolSection/ArmsInterventionsModule/ArmGroupList/ArmGroup/ArmGroupInterventionNameList/ArmGroupInterventionName
numArmGroupInterventionNames ✗
NumArmGroupInterventionNames		

	FUNC NOccrs(ArmGroupInterventionName)	short (stats)				protocolSection.armsInterventionsModule.armGroups.numArmGroupInterventionNames
/Study/ProtocolSection/ArmsInterventionsModule/ArmGroupList/ArmGroup/NumArmGroupInterventionNames
numArmGroups ✗
NumArmGroups		

    NUMBER-OF-ARM-GROUPS 

	FUNC NOccrs(ArmGroup)	short (stats)	Number of Arms		Required for FRED	protocolSection.armsInterventionsModule.numArmGroups
/Study/ProtocolSection/ArmsInterventionsModule/NumArmGroups
interventions
Intervention	Intervention/Treatment	

	STRUCT	Intervention[]	Interventions		At least one intervention must be specified for INT studies	protocolSection.armsInterventionsModule.interventions
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention
type
InterventionType	Intervention/Treatment Type	

    INTERVENTION-TYPE
    INTERVENTION-TYPES 

	TEXT	enum InterventionType (stats)	Intervention Type		Required for INT. Optional for OBS/EA. Redacted for Withheld studies	protocolSection.armsInterventionsModule.interventions.type
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/InterventionType
name
InterventionName	Intervention Name	

    INTERVENTION
    INTERVENTIONS
    INTERVENTION-NAME 

	TEXT (max 200 chars)	text ✓ (stats)	Intervention Name(s)		Required for INT/OBS/EA	protocolSection.armsInterventionsModule.interventions.name
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/InterventionName
description
InterventionDescription	Intervention Description	

    INTERVENTION-DESCRIPTION 

	MARKUP (max 1000 chars)	markup ✓ (stats)	Intervention Description		Required for FRED. Optional for EA if EA type is "Individual Patients"	protocolSection.armsInterventionsModule.interventions.description
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/InterventionDescription
armGroupLabels
InterventionArmGroupLabel	Arm Group Label for Intervention	

	TEXT	text[] ✓ (stats)		
Arm/Group and Intervention Cross Reference
		protocolSection.armsInterventionsModule.interventions.armGroupLabels
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/InterventionArmGroupLabelList/InterventionArmGroupLabel
numInterventionArmGroupLabels ✗
NumInterventionArmGroupLabels		

	FUNC NOccrs(InterventionArmGroupLabel)	short (stats)				protocolSection.armsInterventionsModule.interventions.numInterventionArmGroupLabels
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/NumInterventionArmGroupLabels
otherNames
InterventionOtherName	Other Intervention Name	

    INTERVENTION-OTHER-NAME 

	TEXT	text[] ✓ (stats)	Other Intervention Name(s)		Conditionally required (if InterventionType is drug). Optional for EA if EA type is "Individual Patients"	protocolSection.armsInterventionsModule.interventions.otherNames
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/InterventionOtherNameList/InterventionOtherName
numInterventionOtherNames ✗
NumInterventionOtherNames	Other Intervention Name Count	

	FUNC NOccrs(InterventionOtherName)	short (stats)			Internally calculated	protocolSection.armsInterventionsModule.interventions.numInterventionOtherNames
/Study/ProtocolSection/ArmsInterventionsModule/InterventionList/Intervention/NumInterventionOtherNames
numInterventions ✗
NumInterventions	Number of Interventions	

    NUMBER-OF-INTERVENTIONS 

	FUNC NOccrs(Intervention)	short (stats)			Internally calculated	protocolSection.armsInterventionsModule.numInterventions
/Study/ProtocolSection/ArmsInterventionsModule/NumInterventions
outcomesModule
OutcomesModule	Outcome Measure Module	

	STRUCT	OutcomesModule	Outcome Measures		For INT/OBS. There should be at least one Primary Outcome Measure	protocolSection.outcomesModule
/Study/ProtocolSection/OutcomesModule
primaryOutcomes
PrimaryOutcome		

	STRUCT	Outcome[]	Primary Outcome Measure Information			protocolSection.outcomesModule.primaryOutcomes
/Study/ProtocolSection/OutcomesModule/PrimaryOutcomeList/PrimaryOutcome
measure
PrimaryOutcomeMeasure	Primary Outcome Title	

    PRIMARY-OUTCOME-MEASURE 

	TEXT (max 255 chars)	text ✓ (stats)	Title		Required	protocolSection.outcomesModule.primaryOutcomes.measure
/Study/ProtocolSection/OutcomesModule/PrimaryOutcomeList/PrimaryOutcome/PrimaryOutcomeMeasure
description
PrimaryOutcomeDescription	Primary Outcome Measure Description	

	MARKUP (max 999 chars)	markup ✓ (stats)	Description			protocolSection.outcomesModule.primaryOutcomes.description
/Study/ProtocolSection/OutcomesModule/PrimaryOutcomeList/PrimaryOutcome/PrimaryOutcomeDescription
timeFrame
PrimaryOutcomeTimeFrame	Primary Outcome Measure Time Frame	

	TEXT (max 255 chars)	text ✓ (stats)	Time Frame		Required	protocolSection.outcomesModule.primaryOutcomes.timeFrame
/Study/ProtocolSection/OutcomesModule/PrimaryOutcomeList/PrimaryOutcome/PrimaryOutcomeTimeFrame
numPrimaryOutcomes ✗
NumPrimaryOutcomes	Number of Primary Outcome Measures	

    NUMBER-OF-PRIMARY-OMS 

	FUNC NOccrs(PrimaryOutcome)	short (stats)			Internally calculated	protocolSection.outcomesModule.numPrimaryOutcomes
/Study/ProtocolSection/OutcomesModule/NumPrimaryOutcomes
secondaryOutcomes
SecondaryOutcome	Secondary Outcome Measure	

	STRUCT	Outcome[]	Secondary Outcome Measure Information		Conditional if there is Secondary Outcome Measure, the following fields, title, timeframe, are required.	protocolSection.outcomesModule.secondaryOutcomes
/Study/ProtocolSection/OutcomesModule/SecondaryOutcomeList/SecondaryOutcome
measure
SecondaryOutcomeMeasure	Secondary Outcome Measure Title	

    SECONDARY-OUTCOME-MEASURE 

	TEXT	text ✓ (stats)	Title			protocolSection.outcomesModule.secondaryOutcomes.measure
/Study/ProtocolSection/OutcomesModule/SecondaryOutcomeList/SecondaryOutcome/SecondaryOutcomeMeasure
description
SecondaryOutcomeDescription	Secondary Outcome Measure Description	

	MARKUP	markup ✓ (stats)	Description			protocolSection.outcomesModule.secondaryOutcomes.description
/Study/ProtocolSection/OutcomesModule/SecondaryOutcomeList/SecondaryOutcome/SecondaryOutcomeDescription
timeFrame
SecondaryOutcomeTimeFrame	Secondary Outcome Measure Time Frame	

	TEXT	text ✓ (stats)	Time Frame			protocolSection.outcomesModule.secondaryOutcomes.timeFrame
/Study/ProtocolSection/OutcomesModule/SecondaryOutcomeList/SecondaryOutcome/SecondaryOutcomeTimeFrame
numSecondaryOutcomes ✗
NumSecondaryOutcomes	Number of Secondary Measure Outcomes	

    NUMBER-OF-SECONDARY-OMS 

	FUNC NOccrs(SecondaryOutcome)	short (stats)			Internally calculated	protocolSection.outcomesModule.numSecondaryOutcomes
/Study/ProtocolSection/OutcomesModule/NumSecondaryOutcomes
otherOutcomes
OtherOutcome	Other Outcome Measure	

	STRUCT	Outcome[]	Other Pre-specified Outcome Measures		Optional but if there is Other Outcome Measure, the following fields, title, timeframe, are required.	protocolSection.outcomesModule.otherOutcomes
/Study/ProtocolSection/OutcomesModule/OtherOutcomeList/OtherOutcome
measure
OtherOutcomeMeasure	Other Outcome Measure Measure Title	

	TEXT	text ✓ (stats)	Title			protocolSection.outcomesModule.otherOutcomes.measure
/Study/ProtocolSection/OutcomesModule/OtherOutcomeList/OtherOutcome/OtherOutcomeMeasure
description
OtherOutcomeDescription	Other Outcome Measure Description	

	MARKUP	markup ✓ (stats)	Description			protocolSection.outcomesModule.otherOutcomes.description
/Study/ProtocolSection/OutcomesModule/OtherOutcomeList/OtherOutcome/OtherOutcomeDescription
timeFrame
OtherOutcomeTimeFrame	Other Outcome Measure Time Frame	

	TEXT	text ✓ (stats)	Time Frame			protocolSection.outcomesModule.otherOutcomes.timeFrame
/Study/ProtocolSection/OutcomesModule/OtherOutcomeList/OtherOutcome/OtherOutcomeTimeFrame
numOtherOutcomes ✗
NumOtherOutcomes	Num of Other Outcome Measures	

	FUNC NOccrs(OtherOutcome)	short (stats)			Internally calculated	protocolSection.outcomesModule.numOtherOutcomes
/Study/ProtocolSection/OutcomesModule/NumOtherOutcomes
numOutcomes ✗
NumOutcomes	Number of Outcome Measures	

	FUNC NOccrs(PrimaryOutcome, SecondaryOutcome, OtherOutcome)	short (stats)			Internally calculated	protocolSection.outcomesModule.numOutcomes
/Study/ProtocolSection/OutcomesModule/NumOutcomes
eligibilityModule
EligibilityModule		

	STRUCT	EligibilityModule	Eligibility			protocolSection.eligibilityModule
/Study/ProtocolSection/EligibilityModule
eligibilityCriteria
EligibilityCriteria	Inclusion and exclusion eligibility criteria	

	MARKUP (max 20000 chars)	markup ✓ (stats)	Eligibility Criteria		Required for INT/OBS/EA. Optional if EA type is "Individual Patients"	protocolSection.eligibilityModule.eligibilityCriteria
/Study/ProtocolSection/EligibilityModule/EligibilityCriteria
healthyVolunteers
HealthyVolunteers	Accepts Healthy Volunteers	

    HEALTHY-VOLUNTEERS 

	BOOLEAN	boolean (stats)	Accepts Healthy Volunteers		Required for FRED, ELG NOTE and ACT	protocolSection.eligibilityModule.healthyVolunteers
/Study/ProtocolSection/EligibilityModule/HealthyVolunteers
sex
Sex	Sex/Gender	

    Gender
    GENDER 

	TEXT	enum Sex (stats)	Sex/Gender		Required for FRED, ELG NOTE and ACT. Redacted for Withheld studies
Optional for EA if EA type is "Individual Patients"	protocolSection.eligibilityModule.sex
/Study/ProtocolSection/EligibilityModule/Sex
genderBased
GenderBased	Gender-Based Eligibility	

	BOOLEAN	boolean (stats)	Gender Based		Redacted for Withheld studies	protocolSection.eligibilityModule.genderBased
/Study/ProtocolSection/EligibilityModule/GenderBased
genderDescription
GenderDescription	Gender Description	

	MARKUP (max 1000 chars)	markup ✓ (stats)	Gender Eligibility Description			protocolSection.eligibilityModule.genderDescription
/Study/ProtocolSection/EligibilityModule/GenderDescription
minimumAge
MinimumAge	Minimum Age	

    MINIMUM-AGE 

	TIME	NormalizedTime (stats)	Minimum Age		Required but data can be [Missing] if N/A (No limit) is selected	protocolSection.eligibilityModule.minimumAge
/Study/ProtocolSection/EligibilityModule/MinimumAge
maximumAge
MaximumAge	Maximum Age	

    MAXIMUM-AGE 

	TIME	NormalizedTime (stats)	Maximum Age		Required but data can be [Missing] if N/A (No limit) is selected	protocolSection.eligibilityModule.maximumAge
/Study/ProtocolSection/EligibilityModule/MaximumAge
stdAges
StdAge	Age Group	

    AGE-GROUP 

	TEXT	enum StandardAge[] (stats)		
Ingest calculated the StdAge if there is minimumAge and/or maximimumAge entered. Redacted for Withheld studies
		protocolSection.eligibilityModule.stdAges
/Study/ProtocolSection/EligibilityModule/StdAgeList/StdAge
numStdAges ✗
NumStdAges	Num Std Measure Ages	

	FUNC NOccrs(StdAge)	short (stats)				protocolSection.eligibilityModule.numStdAges
/Study/ProtocolSection/EligibilityModule/NumStdAges
studyPopulation
StudyPopulation	Study Population Description	

	MARKUP (max 1000 chars)	markup ✓ (stats)	Study Population Description (For observational studies only)		Required for OBS only. Redacted for Withheld studies.	protocolSection.eligibilityModule.studyPopulation
/Study/ProtocolSection/EligibilityModule/StudyPopulation
samplingMethod
SamplingMethod	Sampling Method	

	TEXT	enum SamplingMethod (stats)	Sampling Method (For observational studies only)		Required for OBS only. Redacted for Withheld studies.	protocolSection.eligibilityModule.samplingMethod
/Study/ProtocolSection/EligibilityModule/SamplingMethod
contactsLocationsModule
ContactsLocationsModule	Contacts Locations Measure Module	

	STRUCT	ContactsLocationsModule	Contacts, Locations, and Investigator Information			protocolSection.contactsLocationsModule
/Study/ProtocolSection/ContactsLocationsModule
centralContacts
CentralContact	Central Contact Person or Optional Central Contact Backup	

	STRUCT	Contact[]	Central Contact Person or Optional Central Contact Backup		Last Name/Official Title, Phone, Email are required for Central Contact Person. Redacted for Withheld studies
Optional Central Contact Backup	protocolSection.contactsLocationsModule.centralContacts
/Study/ProtocolSection/ContactsLocationsModule/CentralContactList/CentralContact
name
CentralContactName	Central Contact Name	

	TEXT	text (stats)	First Name & Middle Initial & Last Name or Official Title & Degree			protocolSection.contactsLocationsModule.centralContacts.name
/Study/ProtocolSection/ContactsLocationsModule/CentralContactList/CentralContact/CentralContactName
role
CentralContactRole	Central Contact Role	

	TEXT	enum ContactRole (stats)		
Role for any Central Contact added
		protocolSection.contactsLocationsModule.centralContacts.role
/Study/ProtocolSection/ContactsLocationsModule/CentralContactList/CentralContact/CentralContactRole
phone
CentralContactPhone	Central Contact Phone	

	TEXT	text (stats)	Phone			protocolSection.contactsLocationsModule.centralContacts.phone
/Study/ProtocolSection/ContactsLocationsModule/CentralContactList/CentralContact/CentralContactPhone
phoneExt
CentralContactPhoneExt	Central Contact Phone Ext	

	TEXT	text (stats)	Ext			protocolSection.contactsLocationsModule.centralContacts.phoneExt
/Study/ProtocolSection/ContactsLocationsModule/CentralContactList/CentralContact/CentralContactPhoneExt
email
CentralContactEMail	Central Contact EMail	

	TEXT	text (stats)	Email			protocolSection.contactsLocationsModule.centralContacts.email
/Study/ProtocolSection/ContactsLocationsModule/CentralContactList/CentralContact/CentralContactEMail
numCentralContacts ✗
NumCentralContacts	Number of Central Contacts	

	FUNC NOccrs(CentralContact)	short (stats)				protocolSection.contactsLocationsModule.numCentralContacts
/Study/ProtocolSection/ContactsLocationsModule/NumCentralContacts
overallOfficials
OverallOfficial	Overall Study Official	

	STRUCT	Official[]	Overall Study Officials			protocolSection.contactsLocationsModule.overallOfficials
/Study/ProtocolSection/ContactsLocationsModule/OverallOfficialList/OverallOfficial
name
OverallOfficialName	Overall Official Name	

	TEXT	text (stats)	First Name & Middle Initial & Last Name & Degree			protocolSection.contactsLocationsModule.overallOfficials.name
/Study/ProtocolSection/ContactsLocationsModule/OverallOfficialList/OverallOfficial/OverallOfficialName
affiliation
OverallOfficialAffiliation	Overall Official Affiliation	

	TEXT (max 255 chars)	text (stats)	Organizational Affiliation			protocolSection.contactsLocationsModule.overallOfficials.affiliation
/Study/ProtocolSection/ContactsLocationsModule/OverallOfficialList/OverallOfficial/OverallOfficialAffiliation
role
OverallOfficialRole	Overall Official Role	

	TEXT	enum OfficialRole (stats)	Official's Role		Redacted for Withheld studies. PRS GUI has the following options
•[Non-Selected]
•Study Chair
•Study Director
•Study Principal Investigator	protocolSection.contactsLocationsModule.overallOfficials.role
/Study/ProtocolSection/ContactsLocationsModule/OverallOfficialList/OverallOfficial/OverallOfficialRole
numOverallOfficials ✗
NumOverallOfficials	Number of Overall Officials	

	FUNC NOccrs(OverallOfficial)	short (stats)				protocolSection.contactsLocationsModule.numOverallOfficials
/Study/ProtocolSection/ContactsLocationsModule/NumOverallOfficials
locations ⤷
Location	Location	

	STRUCT	Location[]	Facility Information			protocolSection.contactsLocationsModule.locations
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location
facility
LocationFacility	Facility Name	

    LOCATION-NAME
    FACILITY 

	TEXT (max 254 chars)	text (stats)	Facility Name		Required for FRED. Redacted for Withheld studies	protocolSection.contactsLocationsModule.locations.facility
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationFacility
status
LocationStatus	Individual site recruitment status	

    LOCATION-STATUS 

	TEXT	enum RecruitmentStatus (stats)	Individual Site Status		Required. Redacted for Withheld studies	protocolSection.contactsLocationsModule.locations.status
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationStatus
city
LocationCity	City	

    LOCATION-CITY
    CITY 

	TEXT	text (stats)	City		Required. Redacted for Withheld studies	protocolSection.contactsLocationsModule.locations.city
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationCity
state
LocationState	State	

    LOCATION-STATE
    STATE 

	TEXT	text (stats)	State/Province		Required if US location. Redacted for Withheld studies	protocolSection.contactsLocationsModule.locations.state
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationState
zip
LocationZip	Zipcode	

    LOCATION-ZIP 

	TEXT	text (stats)	ZIP/Postal Code		Required if US location. Redacted for Withheld studies	protocolSection.contactsLocationsModule.locations.zip
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationZip
country
LocationCountry	Country	

    LOCATION-COUNTRY
    COUNTRY
    COUNTRIES 

	TEXT	text (stats)	Country		Required. Redacted for Withheld studies	protocolSection.contactsLocationsModule.locations.country
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationCountry
contacts
LocationContact	Facility Contact	

	STRUCT	Contact[]	Facility Contact or Facility Contact Backup			protocolSection.contactsLocationsModule.locations.contacts
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationContactList/LocationContact
name
LocationContactName	Location Contact Name	

	TEXT	text (stats)	First Name & Middle Initial & Last Name & Degree			protocolSection.contactsLocationsModule.locations.contacts.name
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationContactList/LocationContact/LocationContactName
role
LocationContactRole	Location Contact Role	

	TEXT	enum ContactRole (stats)	Investigators			protocolSection.contactsLocationsModule.locations.contacts.role
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationContactList/LocationContact/LocationContactRole
phone
LocationContactPhone	Location Contact Phone	

	TEXT	text (stats)	Phone			protocolSection.contactsLocationsModule.locations.contacts.phone
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationContactList/LocationContact/LocationContactPhone
phoneExt
LocationContactPhoneExt	Location Contact Phone Ext	

	TEXT	text (stats)	Ext			protocolSection.contactsLocationsModule.locations.contacts.phoneExt
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationContactList/LocationContact/LocationContactPhoneExt
email
LocationContactEMail	Location Contact EMail	

	TEXT	text (stats)	Email			protocolSection.contactsLocationsModule.locations.contacts.email
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationContactList/LocationContact/LocationContactEMail
numLocationContacts ✗
NumLocationContacts	Number of Individual Location Contacts	

	FUNC NOccrs(LocationContact)	short (stats)				protocolSection.contactsLocationsModule.locations.numLocationContacts
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/NumLocationContacts
countryCode ✗
LocationCountryCode	ISO Country Code	

	FUNC CountryCode(LocationCountry)	keyword (stats)				protocolSection.contactsLocationsModule.locations.countryCode
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationCountryCode
geoPoint
LocationGeoPoint	Location Geo Point	

	FUNC GeoPoint(LocationCity, LocationState, LocationZip, LocationCountry, LocationLatitude, LocationLongitude, OverallStatus, LastUpdatePostDate)	GeoPoint (stats)				protocolSection.contactsLocationsModule.locations.geoPoint
/Study/ProtocolSection/ContactsLocationsModule/LocationList/Location/LocationGeoPoint
numLocations ✗
NumLocations	Number of Individual Sites/Locations	

    NUMBER-OF-LOCATIONS 

	FUNC NOccrs(Location)	short (stats)				protocolSection.contactsLocationsModule.numLocations
/Study/ProtocolSection/ContactsLocationsModule/NumLocations
numUniqueLocationCountries ✗
NumUniqueLocationCountries	Num Unique Measure Location Countries	

    NUMBER-OF-COUNTRIES 

	FUNC NUniq(LocationCountry)	short (stats)				protocolSection.contactsLocationsModule.numUniqueLocationCountries
/Study/ProtocolSection/ContactsLocationsModule/NumUniqueLocationCountries
referencesModule
ReferencesModule		

	STRUCT	ReferencesModule	References			protocolSection.referencesModule
/Study/ProtocolSection/ReferencesModule
references
Reference	Citations to publications related to the protocol	

	STRUCT	Reference[]	Citations			protocolSection.referencesModule.references
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference
pmid
ReferencePMID	PubMed Identifier	

    PUBMED-IDS 

	TEXT	text (stats)	PubMed Identifier			protocolSection.referencesModule.references.pmid
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/ReferencePMID
type
ReferenceType	Reference Type	

	TEXT	enum ReferenceType (stats)	Results Reference			protocolSection.referencesModule.references.type
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/ReferenceType
citation
ReferenceCitation	Reference Citation	

    CITATIONS 

	TEXT (max 2000 chars)	text ✓ (stats)	Citation			protocolSection.referencesModule.references.citation
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/ReferenceCitation
retractions
Retraction		

	STRUCT	Retraction[]				protocolSection.referencesModule.references.retractions
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/RetractionList/Retraction
pmid
RetractionPMID	PMID for Publication Retraction	

	TEXT	text (stats)		
PMID for publication retraction
		protocolSection.referencesModule.references.retractions.pmid
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/RetractionList/Retraction/RetractionPMID
source
RetractionSource		

	TEXT	text ✓ (stats)				protocolSection.referencesModule.references.retractions.source
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/RetractionList/Retraction/RetractionSource
numRetractionsForRef ✗
NumRetractionsForRef	Number of Reference Retractions	

	FUNC NOccrs(Retraction)	short (stats)				protocolSection.referencesModule.references.numRetractionsForRef
/Study/ProtocolSection/ReferencesModule/ReferenceList/Reference/NumRetractionsForRef
numReferences ✗
NumReferences	Number of References to an Expanded Access Study	

    NUMBER-OF-CITATIONS 

	FUNC NOccrs(Reference)	short (stats)				protocolSection.referencesModule.numReferences
/Study/ProtocolSection/ReferencesModule/NumReferences
numRetractionsAllRefs ✗
NumRetractionsAllRefs	Number of Retractions All Refs	

	FUNC NOccrs(Retraction)	short (stats)				protocolSection.referencesModule.numRetractionsAllRefs
/Study/ProtocolSection/ReferencesModule/NumRetractionsAllRefs
seeAlsoLinks
SeeAlsoLink	See Also Measure Link	

	STRUCT	SeeAlsoLink[]	Links			protocolSection.referencesModule.seeAlsoLinks
/Study/ProtocolSection/ReferencesModule/SeeAlsoLinkList/SeeAlsoLink
label
SeeAlsoLinkLabel	See Also Link Label Title	

    LINK-LABELS 

	MARKUP (max 254 chars)	markup ✓ (stats)	Description			protocolSection.referencesModule.seeAlsoLinks.label
/Study/ProtocolSection/ReferencesModule/SeeAlsoLinkList/SeeAlsoLink/SeeAlsoLinkLabel
url
SeeAlsoLinkURL		

    LINKS 

	TEXT (max 3999 chars)	text (stats)	URL		EA does not have this data element	protocolSection.referencesModule.seeAlsoLinks.url
/Study/ProtocolSection/ReferencesModule/SeeAlsoLinkList/SeeAlsoLink/SeeAlsoLinkURL
numSeeAlsoLinks ✗
NumSeeAlsoLinks	Number of See Also Links	

	FUNC NOccrs(SeeAlsoLink)	short (stats)			EA does not have this data element	protocolSection.referencesModule.numSeeAlsoLinks
/Study/ProtocolSection/ReferencesModule/NumSeeAlsoLinks
availIpds
AvailIPD	Available IPD Information	

	STRUCT	AvailIpd[]		
The individual participant data (IPD) sets and supporting information that are being shared for the study.
	EA does not have this data element	protocolSection.referencesModule.availIpds
/Study/ProtocolSection/ReferencesModule/AvailIPDList/AvailIPD
id
AvailIPDId	Available IPD ID	

	TEXT (max 30 chars)	text ✓ (stats)	Available IPD/Information Identifier		EA does not have this data element	protocolSection.referencesModule.availIpds.id
/Study/ProtocolSection/ReferencesModule/AvailIPDList/AvailIPD/AvailIPDId
type
AvailIPDType	Available IPD Type	

	TEXT (max 254 chars)	text ✓ (stats)	Available IPD/Information Type		EA does not have this data element. Types are stored as character strings, including user-specified other type (max 254 characters)	protocolSection.referencesModule.availIpds.type
/Study/ProtocolSection/ReferencesModule/AvailIPDList/AvailIPD/AvailIPDType
url
AvailIPDURL	Available IPD URL	

	TEXT (max 3999 chars)	text (stats)	Available IPD/Information URL		For INT/OBS. EA does not have this data element	protocolSection.referencesModule.availIpds.url
/Study/ProtocolSection/ReferencesModule/AvailIPDList/AvailIPD/AvailIPDURL
comment
AvailIPDComment	Available IPD Comment	

	MARKUP (max 1000 chars)	markup ✓ (stats)	Available IPD/Information Comments		For INT/OBS. EA does not have this data element	protocolSection.referencesModule.availIpds.comment
/Study/ProtocolSection/ReferencesModule/AvailIPDList/AvailIPD/AvailIPDComment
numAvailIpDs ✗
NumAvailIPDs	Number of Available IPDs	

	FUNC NOccrs(AvailIPD)	short (stats)			For INT/OBS. EA does not have this data element	protocolSection.referencesModule.numAvailIpDs
/Study/ProtocolSection/ReferencesModule/NumAvailIPDs
ipdSharingStatementModule
IPDSharingStatementModule	IPDSharing Statement Module	

	STRUCT	IpdSharingStatementModule	IPD Sharing Statement		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule
/Study/ProtocolSection/IPDSharingStatementModule
ipdSharing
IPDSharing	Plan to Share IPD	

	TEXT	enum IpdSharing (stats)	Plan to Share IPD		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule.ipdSharing
/Study/ProtocolSection/IPDSharingStatementModule/IPDSharing
description
IPDSharingDescription	IPD Sharing Description	

	MARKUP (max 1000 chars)	markup ✓ (stats)	IPD Sharing Plan Description		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule.description
/Study/ProtocolSection/IPDSharingStatementModule/IPDSharingDescription
infoTypes
IPDSharingInfoType	IPDSharing Info Type	

	TEXT	enum IpdSharingInfoType[] (stats)	IPD Sharing Supporting Information Type		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule.infoTypes
/Study/ProtocolSection/IPDSharingStatementModule/IPDSharingInfoTypeList/IPDSharingInfoType
numIpdSharingInfoTypes ✗
NumIPDSharingInfoTypes	Number of IPDSharing Info Types	

	FUNC NOccrs(IPDSharingInfoType)	short (stats)		
Number of IPD Types Selected
		protocolSection.ipdSharingStatementModule.numIpdSharingInfoTypes
/Study/ProtocolSection/IPDSharingStatementModule/NumIPDSharingInfoTypes
timeFrame
IPDSharingTimeFrame	IPDSharing Time Frame	

	MARKUP (max 1000 chars)	markup ✓ (stats)	IPD Sharing Time Frame		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule.timeFrame
/Study/ProtocolSection/IPDSharingStatementModule/IPDSharingTimeFrame
accessCriteria
IPDSharingAccessCriteria	IPDSharing Access Criteria	

	MARKUP (max 1000 chars)	markup ✓ (stats)	IPD Sharing Access Criteria		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule.accessCriteria
/Study/ProtocolSection/IPDSharingStatementModule/IPDSharingAccessCriteria
url
IPDSharingURL	IPD Sharing URL	

	TEXT (max 3999 chars)	text (stats)	IPD Sharing URL		For INT/OBS. EA does not have this data element	protocolSection.ipdSharingStatementModule.url
/Study/ProtocolSection/IPDSharingStatementModule/IPDSharingURL





Results Section
Field Name
Piece Name	Field Title	Alt Piece Names	Classic Type	Data Type	Definition	Description	Notes	Index Field
Classic XPath
resultsSection
ResultsSection	Results Section	

	STRUCT	ResultsSection	Study Results			resultsSection
/Study/ResultsSection
participantFlowModule
ParticipantFlowModule	Participant Flow Module	

	STRUCT	ParticipantFlowModule	Participant Flow			resultsSection.participantFlowModule
/Study/ResultsSection/ParticipantFlowModule
preAssignmentDetails
FlowPreAssignmentDetails	Pre-assignment Details	

	MARKUP (max 500 chars)	markup ✓ (stats)	Pre-assignment Details		Conditionally Required	resultsSection.participantFlowModule.preAssignmentDetails
/Study/ResultsSection/ParticipantFlowModule/FlowPreAssignmentDetails
recruitmentDetails
FlowRecruitmentDetails	Recruitment Details	

	MARKUP (max 500 chars)	markup ✓ (stats)	Recruitment Details			resultsSection.participantFlowModule.recruitmentDetails
/Study/ResultsSection/ParticipantFlowModule/FlowRecruitmentDetails
typeUnitsAnalyzed
FlowTypeUnitsAnalyzed	Type of Unit Analyzed	

	TEXT (max 40 chars)	text ✓ (stats)	Type of Units Assigned		Conditionally Required	resultsSection.participantFlowModule.typeUnitsAnalyzed
/Study/ResultsSection/ParticipantFlowModule/FlowTypeUnitsAnalyzed
groups
FlowGroup	Arm/Group Information	

	STRUCT	FlowGroup[]	Arm/Group Information		Required	resultsSection.participantFlowModule.groups
/Study/ResultsSection/ParticipantFlowModule/FlowGroupList/FlowGroup
id
FlowGroupId	Arm/Group ID	

	TEXT	text ✓ (stats)		
Arm/Group ID generated by PRS
	system generated	resultsSection.participantFlowModule.groups.id
/Study/ResultsSection/ParticipantFlowModule/FlowGroupList/FlowGroup/FlowGroupId
title
FlowGroupTitle	Arm/Group Title	

	TEXT	text ✓ (stats)	Arm/Group Title		Required	resultsSection.participantFlowModule.groups.title
/Study/ResultsSection/ParticipantFlowModule/FlowGroupList/FlowGroup/FlowGroupTitle
description
FlowGroupDescription	Arm/Group Description	

	MARKUP (max 1500 chars)	markup ✓ (stats)	Arm/Group Description		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.participantFlowModule.groups.description
/Study/ResultsSection/ParticipantFlowModule/FlowGroupList/FlowGroup/FlowGroupDescription
numFlowGroups ✗
NumFlowGroups	Number of Arm/Groups	

	FUNC NOccrs(FlowGroup)	short (stats)		
Number of Arm/Group
	system generated	resultsSection.participantFlowModule.numFlowGroups
/Study/ResultsSection/ParticipantFlowModule/NumFlowGroups
periods
FlowPeriod	Period	

	STRUCT	FlowPeriod[]	Period(s)			resultsSection.participantFlowModule.periods
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod
title
FlowPeriodTitle	Period Title	

	TEXT (max 40 chars)	text ✓ (stats)	Period Title		Required	resultsSection.participantFlowModule.periods.title
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowPeriodTitle
milestones
FlowMilestone	Milestone	

	STRUCT	FlowMilestone[]	Additional Milestone(s)		Required for Milestones "Started" and "Completed"	resultsSection.participantFlowModule.periods.milestones
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone
type
FlowMilestoneType	Milestone Title	

	TEXT (max 100 chars)	text ✓ (stats)	Started			resultsSection.participantFlowModule.periods.milestones.type
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowMilestoneType
comment
FlowMilestoneComment	Milestone Comment	

	MARKUP (max 500 chars)	markup ✓ (stats)	Comments			resultsSection.participantFlowModule.periods.milestones.comment
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowMilestoneComment
achievements
FlowAchievement	Milestone Data	

	STRUCT	FlowStats[]		
Milestone Data (per arm/group)
		resultsSection.participantFlowModule.periods.milestones.achievements
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowAchievementList/FlowAchievement
groupId
FlowAchievementGroupId	Milestone Arm/Group ID	

	TEXT	text ✓ (stats)		
ID
	system generated	resultsSection.participantFlowModule.periods.milestones.achievements.groupId
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowAchievementList/FlowAchievement/FlowAchievementGroupId
comment
FlowAchievementComment	Milestone Arm/Group Comment	

	MARKUP (max 500 chars)	markup ✓ (stats)	Comments			resultsSection.participantFlowModule.periods.milestones.achievements.comment
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowAchievementList/FlowAchievement/FlowAchievementComment
numSubjects
FlowAchievementNumSubjects	Number of Milestone Arm/Group Participants	

	TEXT	text ✓ (stats)	Milestone Data		Conditionally Required	resultsSection.participantFlowModule.periods.milestones.achievements.numSubjects
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowAchievementList/FlowAchievement/FlowAchievementNumSubjects
numUnits
FlowAchievementNumUnits	Number of Units	

	TEXT	text ✓ (stats)	Milestone Data		Conditionally Required	resultsSection.participantFlowModule.periods.milestones.achievements.numUnits
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/FlowAchievementList/FlowAchievement/FlowAchievementNumUnits
numFlowAchievements ✗
NumFlowAchievements	Number of Arm/Groups per Milestone	

	FUNC NOccrs(FlowAchievement)	short (stats)		
Number of Arms/Groups (for each milestone)
	system generated	resultsSection.participantFlowModule.periods.milestones.numFlowAchievements
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowMilestoneList/FlowMilestone/NumFlowAchievements
numFlowMilestones ✗
NumFlowMilestones	Number of Milestones	

	FUNC NOccrs(FlowMilestone)	short (stats)		
Number of milestones within a period
	system generated	resultsSection.participantFlowModule.periods.numFlowMilestones
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/NumFlowMilestones
dropWithdraws
FlowDropWithdraw	Reason Not Completed	

	STRUCT	DropWithdraw[]	Reason Not Completed			resultsSection.participantFlowModule.periods.dropWithdraws
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw
type
FlowDropWithdrawType	Reason Not Completed Type	

	TEXT (max 100 chars)	text ✓ (stats)	Reason Not Completed Type		Conditionally Required	resultsSection.participantFlowModule.periods.dropWithdraws.type
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/FlowDropWithdrawType
comment
FlowDropWithdrawComment	Description of Reason Not Completed	

	MARKUP (max 100 chars)	markup ✓ (stats)		
A brief description of the reason for non-completion, if "Other" Reason Not Completed Type is selected.
	Conditionally Required	resultsSection.participantFlowModule.periods.dropWithdraws.comment
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/FlowDropWithdrawComment
reasons
FlowReason		

	STRUCT	FlowStats[]		
Reason for Not Completed per arm/group
		resultsSection.participantFlowModule.periods.dropWithdraws.reasons
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/FlowReasonList/FlowReason
groupId
FlowReasonGroupId	Reason Group ID	

	TEXT	text ✓ (stats)		
Internally generated ID for reason not completed per arm/group
	system generated	resultsSection.participantFlowModule.periods.dropWithdraws.reasons.groupId
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/FlowReasonList/FlowReason/FlowReasonGroupId
comment
FlowReasonComment		

	MARKUP	markup ✓ (stats)	Reason Not Completed Data		not used	resultsSection.participantFlowModule.periods.dropWithdraws.reasons.comment
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/FlowReasonList/FlowReason/FlowReasonComment
numSubjects
FlowReasonNumSubjects	Reason Group Number of Subjects	

	TEXT	text ✓ (stats)	Reason Not Completed Data		Conditionally Required	resultsSection.participantFlowModule.periods.dropWithdraws.reasons.numSubjects
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/FlowReasonList/FlowReason/FlowReasonNumSubjects
numFlowReasons ✗
NumFlowReasons	Number of Arm/Group for a Reason Not Completed	

	FUNC NOccrs(FlowReason)	short (stats)		
number of arm/group in reason not completed
	internally calculated	resultsSection.participantFlowModule.periods.dropWithdraws.numFlowReasons
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/FlowDropWithdrawList/FlowDropWithdraw/NumFlowReasons
numFlowDropWithdraws ✗
NumFlowDropWithdraws	Number of Reason Not Completed	

	FUNC NOccrs(FlowDropWithdraw)	short (stats)		
Number of reasons not completed
	internally calculated	resultsSection.participantFlowModule.periods.numFlowDropWithdraws
/Study/ResultsSection/ParticipantFlowModule/FlowPeriodList/FlowPeriod/NumFlowDropWithdraws
numFlowPeriods ✗
NumFlowPeriods	Number of Periods	

	FUNC NOccrs(FlowPeriod)	short (stats)		
Number of periods
	internally calculated	resultsSection.participantFlowModule.numFlowPeriods
/Study/ResultsSection/ParticipantFlowModule/NumFlowPeriods
baselineCharacteristicsModule
BaselineCharacteristicsModule	Baseline Measure Module	

	STRUCT	BaselineCharacteristicsModule	Baseline Characteristics			resultsSection.baselineCharacteristicsModule
/Study/ResultsSection/BaselineCharacteristicsModule
populationDescription
BaselinePopulationDescription	Baseline Analysis Population Description	

	MARKUP (max 500 chars)	markup ✓ (stats)	Baseline Analysis Population Description		Conditionally Required	resultsSection.baselineCharacteristicsModule.populationDescription
/Study/ResultsSection/BaselineCharacteristicsModule/BaselinePopulationDescription
typeUnitsAnalyzed
BaselineTypeUnitsAnalyzed	Type of Units Analyzed	

	TEXT (max 40 chars)	text ✓ (stats)	Type of Units Analyzed		Conditionally Required	resultsSection.baselineCharacteristicsModule.typeUnitsAnalyzed
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineTypeUnitsAnalyzed
groups
BaselineGroup	Arm/Group Information	

	STRUCT	MeasureGroup[]	Arm/Group Information		Required	resultsSection.baselineCharacteristicsModule.groups
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineGroupList/BaselineGroup
id
BaselineGroupId	Arm/Group ID	

	TEXT	text ✓ (stats)		
Internally generated ID
	system generated	resultsSection.baselineCharacteristicsModule.groups.id
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineGroupList/BaselineGroup/BaselineGroupId
title
BaselineGroupTitle	Arm/Group Title	

	TEXT	text ✓ (stats)	Arm/Group Title		Required	resultsSection.baselineCharacteristicsModule.groups.title
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineGroupList/BaselineGroup/BaselineGroupTitle
description
BaselineGroupDescription	Arm/Group Description	

	MARKUP (max 1500 chars)	markup ✓ (stats)	Arm/Group Description		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.baselineCharacteristicsModule.groups.description
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineGroupList/BaselineGroup/BaselineGroupDescription
numBaselineGroups ✗
NumBaselineGroups	Number of Baseline Arm/Groups	

	FUNC NOccrs(BaselineGroup)	short (stats)		
Number of Arm/Groups for Baseline
	internally calculated	resultsSection.baselineCharacteristicsModule.numBaselineGroups
/Study/ResultsSection/BaselineCharacteristicsModule/NumBaselineGroups
denoms
BaselineDenom		

	STRUCT	Denom[]		
Structure for Overall Baseline Measure Data (Row)
		resultsSection.baselineCharacteristicsModule.denoms
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineDenomList/BaselineDenom
units
BaselineDenomUnits	Overall Number of Units Analyzed	

	TEXT (max 40 chars)	text ✓ (stats)	Overall Number of Units Analyzed		Required	resultsSection.baselineCharacteristicsModule.denoms.units
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineDenomList/BaselineDenom/BaselineDenomUnits
counts
BaselineDenomCount		

	STRUCT	DenomCount[]		
Structure for overall number per arm/group
		resultsSection.baselineCharacteristicsModule.denoms.counts
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineDenomList/BaselineDenom/BaselineDenomCountList/BaselineDenomCount
groupId
BaselineDenomCountGroupId	Arm/Group ID	

	TEXT	text ✓ (stats)		
Internally generated ID for each Arm/Group
		resultsSection.baselineCharacteristicsModule.denoms.counts.groupId
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineDenomList/BaselineDenom/BaselineDenomCountList/BaselineDenomCount/BaselineDenomCountGroupId
value
BaselineDenomCountValue		

	TEXT	text ✓ (stats)	Overall Number of Baseline Participants		Conditionally Required	resultsSection.baselineCharacteristicsModule.denoms.counts.value
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineDenomList/BaselineDenom/BaselineDenomCountList/BaselineDenomCount/BaselineDenomCountValue
numBaselineDenoms ✗
NumBaselineDenoms		

	FUNC NOccrs(BaselineDenom)	short (stats)		
Number of BaselineDenomUnits (Row)
		resultsSection.baselineCharacteristicsModule.numBaselineDenoms
/Study/ResultsSection/BaselineCharacteristicsModule/NumBaselineDenoms
measures
BaselineMeasure	Baseline Measure	

	STRUCT	BaselineMeasure[]	Baseline Measure Information			resultsSection.baselineCharacteristicsModule.measures
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure
title
BaselineMeasureTitle	Baseline Measure Title	

	TEXT	text ✓ (stats)	Baseline Measure Title			resultsSection.baselineCharacteristicsModule.measures.title
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureTitle
description
BaselineMeasureDescription	Baseline Measure Title for Study-Specified Measure	

	MARKUP (max 100 chars)	markup ✓ (stats)	Baseline Measure Description		Conditionally Required	resultsSection.baselineCharacteristicsModule.measures.description
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDescription
populationDescription
BaselineMeasurePopulationDescription	Baseline Measure Description	

	MARKUP (max 600 chars)	markup ✓ (stats)	Measure Analysis Population Description			resultsSection.baselineCharacteristicsModule.measures.populationDescription
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasurePopulationDescription
paramType
BaselineMeasureParamType	Baseline Measure Type	

	TEXT	enum MeasureParam (stats)	Measure Type		Required	resultsSection.baselineCharacteristicsModule.measures.paramType
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureParamType
dispersionType
BaselineMeasureDispersionType	Baseline Measure Dispersion/Precision	

	TEXT	enum MeasureDispersionType (stats)	Measure of Dispersion		Required	resultsSection.baselineCharacteristicsModule.measures.dispersionType
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDispersionType
unitOfMeasure
BaselineMeasureUnitOfMeasure	Unit of Measure	

	TEXT (max 40 chars)	text ✓ (stats)	Unit of Measure		Required	resultsSection.baselineCharacteristicsModule.measures.unitOfMeasure
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureUnitOfMeasure
calculatePct
BaselineMeasureCalculatePct	Calculated Percentage	

	BOOLEAN	boolean (stats)		
percentage of BaselineMeasurementValue/BaselineMeasureDenomCountValue
	internally calculated	resultsSection.baselineCharacteristicsModule.measures.calculatePct
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureCalculatePct
denomUnitsSelected
BaselineMeasureDenomUnitsSelected	Type of Units Selected	

	TEXT (max 40 chars)	text ✓ (stats)	Analysis Population Type			resultsSection.baselineCharacteristicsModule.measures.denomUnitsSelected
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDenomUnitsSelected
denoms
BaselineMeasureDenom		

	STRUCT	Denom[]				resultsSection.baselineCharacteristicsModule.measures.denoms
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDenomList/BaselineMeasureDenom
units
BaselineMeasureDenomUnits	Analysis Population Type	

	TEXT	text ✓ (stats)	Number of Units Analyzed			resultsSection.baselineCharacteristicsModule.measures.denoms.units
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDenomList/BaselineMeasureDenom/BaselineMeasureDenomUnits
counts
BaselineMeasureDenomCount		

	STRUCT	DenomCount[]		
number entered for unit of measure
		resultsSection.baselineCharacteristicsModule.measures.denoms.counts
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDenomList/BaselineMeasureDenom/BaselineMeasureDenomCountList/BaselineMeasureDenomCount
groupId
BaselineMeasureDenomCountGroupId		

	TEXT	text ✓ (stats)		
Internally generated ID for each Arm/Group
		resultsSection.baselineCharacteristicsModule.measures.denoms.counts.groupId
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDenomList/BaselineMeasureDenom/BaselineMeasureDenomCountList/BaselineMeasureDenomCount/BaselineMeasureDenomCountGroupId
value
BaselineMeasureDenomCountValue		

	TEXT	text ✓ (stats)	Number of Baseline Participants			resultsSection.baselineCharacteristicsModule.measures.denoms.counts.value
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineMeasureDenomList/BaselineMeasureDenom/BaselineMeasureDenomCountList/BaselineMeasureDenomCount/BaselineMeasureDenomCountValue
numBaselineMeasureDenoms ✗
NumBaselineMeasureDenoms		

	FUNC NOccrs(BaselineMeasureDenom)	short (stats)			internally calculated	resultsSection.baselineCharacteristicsModule.measures.numBaselineMeasureDenoms
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/NumBaselineMeasureDenoms
classes
BaselineClass		

	STRUCT	MeasureClass[]		
Structure for a Baseline Measure ROW
		resultsSection.baselineCharacteristicsModule.measures.classes
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass
title
BaselineClassTitle	Baseline RowTitle	

	TEXT (max 100 chars)	text ✓ (stats)	Category or Row Title		Required if there is more than one row category	resultsSection.baselineCharacteristicsModule.measures.classes.title
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineClassTitle
denoms
BaselineClassDenom		

	STRUCT	Denom[]				resultsSection.baselineCharacteristicsModule.measures.classes.denoms
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineClassDenomList/BaselineClassDenom
units
BaselineClassDenomUnits	Baseline Row Unit of Measure	

	TEXT (max 40 chars)	text ✓ (stats)		
Possible analysis population when data are presented in rows (e.g., if units other than participants are included in baseline, both participants and the units are listed as BaselineClassDenomUnits for the applicable baseline measure)
	Required	resultsSection.baselineCharacteristicsModule.measures.classes.denoms.units
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineClassDenomList/BaselineClassDenom/BaselineClassDenomUnits
counts
BaselineClassDenomCount	Number of Baseline Row Participants	

	STRUCT	DenomCount[]		
Population Analyzed for a Row
		resultsSection.baselineCharacteristicsModule.measures.classes.denoms.counts
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineClassDenomList/BaselineClassDenom/BaselineClassDenomCountList/BaselineClassDenomCount
groupId
BaselineClassDenomCountGroupId		

	TEXT	text ✓ (stats)		
Internal ID per Arm/Group for a Baseline Measure
		resultsSection.baselineCharacteristicsModule.measures.classes.denoms.counts.groupId
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineClassDenomList/BaselineClassDenom/BaselineClassDenomCountList/BaselineClassDenomCount/BaselineClassDenomCountGroupId
value
BaselineClassDenomCountValue		

	TEXT	text ✓ (stats)		
Data per Arm/Group per Baseline Measure per Row
		resultsSection.baselineCharacteristicsModule.measures.classes.denoms.counts.value
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineClassDenomList/BaselineClassDenom/BaselineClassDenomCountList/BaselineClassDenomCount/BaselineClassDenomCountValue
categories
BaselineCategory		

	STRUCT	MeasureCategory[]		
Categories under a Baseline Measure (represented as rows in data table)
		resultsSection.baselineCharacteristicsModule.measures.classes.categories
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory
title
BaselineCategoryTitle	Category Title	

	TEXT (max 50 chars)	text ✓ (stats)	Category or Row Title		Conditionally Required	resultsSection.baselineCharacteristicsModule.measures.classes.categories.title
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineCategoryTitle
measurements
BaselineMeasurement		

	STRUCT	Measurement[]		
Data structure per Arm/Group per Category
	Required	resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement
groupId
BaselineMeasurementGroupId	Arm/Group ID	

	TEXT	text ✓ (stats)		
Internal ID per Arm/Group per Category
		resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements.groupId
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement/BaselineMeasurementGroupId
value
BaselineMeasurementValue	data	

	TEXT	text ✓ (stats)	Baseline Measure Data			resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements.value
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement/BaselineMeasurementValue
spread
BaselineMeasurementSpread	data	

	TEXT	text ✓ (stats)		
Data per Arm/Group per Category. Based on Measure Type and Measure of Dispersion (e.g., Standard Deviation)
		resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements.spread
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement/BaselineMeasurementSpread
lowerLimit
BaselineMeasurementLowerLimit	data	

	TEXT	text ✓ (stats)		
Data per Arm/Group per Category. Based on Measure Type and Measure of Dispersion (e.g., lower limit of Full Range)
		resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements.lowerLimit
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement/BaselineMeasurementLowerLimit
upperLimit
BaselineMeasurementUpperLimit	data	

	TEXT	text ✓ (stats)		
Data per Arm/Group per Category. Based on Measure Type and Measure of Dispersion (e.g., upper limit of Full Range)
		resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements.upperLimit
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement/BaselineMeasurementUpperLimit
comment
BaselineMeasurementComment	Comments for N/A values	

	MARKUP (max 500 chars)	markup ✓ (stats)		
Explain why baseline measure data are not available, if "NA" is reported for Baseline Measure Data.
	Conditionally Required	resultsSection.baselineCharacteristicsModule.measures.classes.categories.measurements.comment
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/BaselineMeasurementList/BaselineMeasurement/BaselineMeasurementComment
numBaselineMeasurements ✗
NumBaselineMeasurements	Number of Arm/Groups	

	FUNC NOccrs(BaselineMeasurement)	short (stats)		
Number of Baseline Arm/Groups (internally calculated)
		resultsSection.baselineCharacteristicsModule.measures.classes.categories.numBaselineMeasurements
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/BaselineCategoryList/BaselineCategory/NumBaselineMeasurements
numBaselineCategories ✗
NumBaselineCategories	Number of Categories	

	FUNC NOccrs(BaselineCategory)	short (stats)		
Number of categories per Baseline Measure
		resultsSection.baselineCharacteristicsModule.measures.classes.numBaselineCategories
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/BaselineClassList/BaselineClass/NumBaselineCategories
numBaselineClasses ✗
NumBaselineClasses		

	FUNC NOccrs(BaselineClass)	short (stats)		
Number of classes (rows) per Baseline Measure (internally calculated)
		resultsSection.baselineCharacteristicsModule.measures.numBaselineClasses
/Study/ResultsSection/BaselineCharacteristicsModule/BaselineMeasureList/BaselineMeasure/NumBaselineClasses
numBaselineMeasures ✗
NumBaselineMeasures	Number of Baseline Measures	

	FUNC NOccrs(BaselineMeasure)	short (stats)		
Number of Baseline Measures (internally calculated)
		resultsSection.baselineCharacteristicsModule.numBaselineMeasures
/Study/ResultsSection/BaselineCharacteristicsModule/NumBaselineMeasures
outcomeMeasuresModule
OutcomeMeasuresModule	Outcome Measure Module	

	STRUCT	OutcomeMeasuresModule	Outcome Measures			resultsSection.outcomeMeasuresModule
/Study/ResultsSection/OutcomeMeasuresModule
outcomeMeasures
OutcomeMeasure	Outcome Measure	

	STRUCT	OutcomeMeasure[]	Outcome Measure Information		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure
type
OutcomeMeasureType	Outcome Measure Type	

	TEXT	enum OutcomeMeasureType (stats)	Outcome Measure Type		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.type
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureType
title
OutcomeMeasureTitle	Outcome Measure title	

	TEXT (max 255 chars)	text ✓ (stats)	Outcome Measure Title		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.title
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureTitle
description
OutcomeMeasureDescription	Outcome Measure Description	

	MARKUP (max 999 chars)	markup ✓ (stats)	Outcome Measure Description		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.description
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureDescription
populationDescription
OutcomeMeasurePopulationDescription	Analysis Population Description	

	MARKUP (max 500 chars)	markup ✓ (stats)	Analysis Population Description		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.populationDescription
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasurePopulationDescription
reportingStatus
OutcomeMeasureReportingStatus	Reporting Status	

	TEXT	enum ReportingStatus (stats)		
Whether there is Outcome Measure Data reported
		resultsSection.outcomeMeasuresModule.outcomeMeasures.reportingStatus
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureReportingStatus
anticipatedPostingDate
OutcomeMeasureAnticipatedPostingDate	Anticipated Reporting Date	

	DATE	PartialDate (stats)	Anticipated Reporting Date			resultsSection.outcomeMeasuresModule.outcomeMeasures.anticipatedPostingDate
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureAnticipatedPostingDate
paramType
OutcomeMeasureParamType	Outcome Measure Data Type	

	TEXT	enum MeasureParam (stats)	Measure Type		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.paramType
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureParamType
dispersionType
OutcomeMeasureDispersionType	Outcome Measure Dispersion/Precision	

	TEXT	text ✓ (stats)	Measure of Dispersion/Precision		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.dispersionType
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureDispersionType
unitOfMeasure
OutcomeMeasureUnitOfMeasure	Unit of Measure	

	TEXT (max 40 chars)	text ✓ (stats)	Unit of Measure		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.unitOfMeasure
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureUnitOfMeasure
calculatePct
OutcomeMeasureCalculatePct	Calculated Percentage	

	BOOLEAN	boolean (stats)		
percentage of OutcomeMeasurementValue/OutcomeMeasureDenomCountValue (internally calculated)
		resultsSection.outcomeMeasuresModule.outcomeMeasures.calculatePct
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureCalculatePct
timeFrame
OutcomeMeasureTimeFrame	Outcome Measure Time Frame	

	TEXT (max 255 chars)	text ✓ (stats)	Outcome Measure Time Frame		Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.timeFrame
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureTimeFrame
typeUnitsAnalyzed
OutcomeMeasureTypeUnitsAnalyzed	Units Analyzed	

	TEXT (max 40 chars)	text ✓ (stats)	Type of Units Analyzed		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.typeUnitsAnalyzed
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureTypeUnitsAnalyzed
denomUnitsSelected
OutcomeMeasureDenomUnitsSelected		

	TEXT	text ✓ (stats)		
OutcomeMeasureTypeUnitsAnalyzed
		resultsSection.outcomeMeasuresModule.outcomeMeasures.denomUnitsSelected
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeMeasureDenomUnitsSelected
groups
OutcomeGroup		

	STRUCT	MeasureGroup[]	Arm/Group Information			resultsSection.outcomeMeasuresModule.outcomeMeasures.groups
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeGroupList/OutcomeGroup
id
OutcomeGroupId		

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.groups.id
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeGroupList/OutcomeGroup/OutcomeGroupId
title
OutcomeGroupTitle		

	TEXT	text ✓ (stats)	Arm/Group Title			resultsSection.outcomeMeasuresModule.outcomeMeasures.groups.title
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeGroupList/OutcomeGroup/OutcomeGroupTitle
description
OutcomeGroupDescription		

	MARKUP	markup ✓ (stats)	Arm/Group Description			resultsSection.outcomeMeasuresModule.outcomeMeasures.groups.description
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeGroupList/OutcomeGroup/OutcomeGroupDescription
numOutcomeGroups ✗
NumOutcomeGroups		

	FUNC NOccrs(OutcomeGroup)	short (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.numOutcomeGroups
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/NumOutcomeGroups
denoms
OutcomeDenom		

	STRUCT	Denom[]				resultsSection.outcomeMeasuresModule.outcomeMeasures.denoms
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeDenomList/OutcomeDenom
units
OutcomeDenomUnits		

	TEXT	text ✓ (stats)	Overall Number of Units Analyzed			resultsSection.outcomeMeasuresModule.outcomeMeasures.denoms.units
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeDenomList/OutcomeDenom/OutcomeDenomUnits
counts
OutcomeDenomCount		

	STRUCT	DenomCount[]				resultsSection.outcomeMeasuresModule.outcomeMeasures.denoms.counts
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeDenomList/OutcomeDenom/OutcomeDenomCountList/OutcomeDenomCount
groupId
OutcomeDenomCountGroupId		

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.denoms.counts.groupId
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeDenomList/OutcomeDenom/OutcomeDenomCountList/OutcomeDenomCount/OutcomeDenomCountGroupId
value
OutcomeDenomCountValue		

	TEXT	text ✓ (stats)	Overall Number of Participants Analyzed			resultsSection.outcomeMeasuresModule.outcomeMeasures.denoms.counts.value
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeDenomList/OutcomeDenom/OutcomeDenomCountList/OutcomeDenomCount/OutcomeDenomCountValue
numOutcomeDenoms ✗
NumOutcomeDenoms		

	FUNC NOccrs(OutcomeDenom)	short (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.numOutcomeDenoms
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/NumOutcomeDenoms
classes
OutcomeClass		

	STRUCT	MeasureClass[]				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass
title
OutcomeClassTitle		

	TEXT	text ✓ (stats)	Category or Row Title			resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.title
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeClassTitle
denoms
OutcomeClassDenom		

	STRUCT	Denom[]				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.denoms
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeClassDenomList/OutcomeClassDenom
units
OutcomeClassDenomUnits		

	TEXT	text ✓ (stats)	Number of Units Analyzed			resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.denoms.units
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeClassDenomList/OutcomeClassDenom/OutcomeClassDenomUnits
counts
OutcomeClassDenomCount		

	STRUCT	DenomCount[]				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.denoms.counts
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeClassDenomList/OutcomeClassDenom/OutcomeClassDenomCountList/OutcomeClassDenomCount
groupId
OutcomeClassDenomCountGroupId		

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.denoms.counts.groupId
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeClassDenomList/OutcomeClassDenom/OutcomeClassDenomCountList/OutcomeClassDenomCount/OutcomeClassDenomCountGroupId
value
OutcomeClassDenomCountValue		

	TEXT	text ✓ (stats)	Number of Participants Analyzed			resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.denoms.counts.value
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeClassDenomList/OutcomeClassDenom/OutcomeClassDenomCountList/OutcomeClassDenomCount/OutcomeClassDenomCountValue
categories
OutcomeCategory	Outcome Category	

	STRUCT	MeasureCategory[]				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory
title
OutcomeCategoryTitle	Category Title	

	TEXT	text ✓ (stats)	Category or Row Title			resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.title
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeCategoryTitle
measurements
OutcomeMeasurement		

	STRUCT	Measurement[]	Outcome Measure Data Table			resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement
groupId
OutcomeMeasurementGroupId	Group ID	

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements.groupId
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement/OutcomeMeasurementGroupId
value
OutcomeMeasurementValue	data	

	TEXT	text ✓ (stats)	Outcome Measure Data			resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements.value
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement/OutcomeMeasurementValue
spread
OutcomeMeasurementSpread	data	

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements.spread
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement/OutcomeMeasurementSpread
lowerLimit
OutcomeMeasurementLowerLimit	data	

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements.lowerLimit
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement/OutcomeMeasurementLowerLimit
upperLimit
OutcomeMeasurementUpperLimit	data	

	TEXT	text ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements.upperLimit
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement/OutcomeMeasurementUpperLimit
comment
OutcomeMeasurementComment	Comments for N/A values	

	MARKUP	markup ✓ (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.measurements.comment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/OutcomeMeasurementList/OutcomeMeasurement/OutcomeMeasurementComment
numOutcomeMeasurements ✗
NumOutcomeMeasurements		

	FUNC NOccrs(OutcomeMeasurement)	short (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.categories.numOutcomeMeasurements
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/OutcomeCategoryList/OutcomeCategory/NumOutcomeMeasurements
numOutcomeCategories ✗
NumOutcomeCategories		

	FUNC NOccrs(OutcomeCategory)	short (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.classes.numOutcomeCategories
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeClassList/OutcomeClass/NumOutcomeCategories
numOutcomeClasses ✗
NumOutcomeClasses		

	FUNC NOccrs(OutcomeClass)	short (stats)				resultsSection.outcomeMeasuresModule.outcomeMeasures.numOutcomeClasses
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/NumOutcomeClasses
analyses
OutcomeAnalysis	Outcome Measure Analysis	

	STRUCT	MeasureAnalysis[]		
Result(s) of scientifically appropriate tests of statistical significance of the primary and secondary outcome measures, if any. Such analyses include: pre-specified in the protocol and/or statistical analysis plan; made public by the sponsor or responsible party; conducted on a primary outcome measure in response to a request made by FDA.
If a statistical analysis is reported "Comparison Group Selection" and "Type of Statistical Test" are required. In addition, one of the following data elements are required with the associated information: "P-Value," "Estimation Parameter," or "Other Statistical Analysis."
	Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis
paramType
OutcomeAnalysisParamType	Estimation Parameter	

	TEXT (max 40 chars)	text ✓ (stats)	Estimation Parameter		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.paramType
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisParamType
paramValue
OutcomeAnalysisParamValue	Estimated Value	

	TEXT	text ✓ (stats)	Estimated Value		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.paramValue
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisParamValue
dispersionType
OutcomeAnalysisDispersionType	Estimation Dispersion Type	

	TEXT	enum AnalysisDispersionType (stats)	Parameter Dispersion Type			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.dispersionType
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisDispersionType
dispersionValue
OutcomeAnalysisDispersionValue	Parameter Dispersion Value	

	TEXT	text ✓ (stats)	Dispersion Value		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.dispersionValue
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisDispersionValue
statisticalMethod
OutcomeAnalysisStatisticalMethod	Statistical Method	

	TEXT (max 40 chars)	text ✓ (stats)	Method			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.statisticalMethod
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisStatisticalMethod
statisticalComment
OutcomeAnalysisStatisticalComment	Statistical Comment	

	MARKUP (max 150 chars)	markup ✓ (stats)	Comments			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.statisticalComment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisStatisticalComment
pValue
OutcomeAnalysisPValue	P-Value	

	TEXT	text ✓ (stats)	P-Value			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.pValue
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisPValue
pValueComment
OutcomeAnalysisPValueComment	P-Value Comment	

	MARKUP (max 250 chars)	markup ✓ (stats)	Comments			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.pValueComment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisPValueComment
ciNumSides
OutcomeAnalysisCINumSides	Number of Sides for Confidence Interval	

	TEXT	enum ConfidenceIntervalNumSides (stats)	Number of Sides		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.ciNumSides
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisCINumSides
ciPctValue
OutcomeAnalysisCIPctValue	Percentage for Confidence Interval	

	TEXT	text ✓ (stats)	Level		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.ciPctValue
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisCIPctValue
ciLowerLimit
OutcomeAnalysisCILowerLimit	Lower Limit for 2-sided Confidence Interval	

	TEXT	text ✓ (stats)	Lower Limit		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.ciLowerLimit
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisCILowerLimit
ciUpperLimit
OutcomeAnalysisCIUpperLimit	Upper Limit for 2-sided Confidence Interval	

	TEXT	text ✓ (stats)	Upper Limit		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.ciUpperLimit
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisCIUpperLimit
ciLowerLimitComment
OutcomeAnalysisCILowerLimitComment	Lower Limit Comment	

	MARKUP	markup ✓ (stats)		
Confidence Interval - lower limit comment
	Not Used	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.ciLowerLimitComment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisCILowerLimitComment
ciUpperLimitComment
OutcomeAnalysisCIUpperLimitComment	Upper Limit Comment	

	MARKUP (max 250 chars)	markup ✓ (stats)		
Confidence Interval - upper limit comment (Explain why the upper limit data are not available, if "NA" is reported as upper-limit of "2-sided" confidence interval.)
	Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.ciUpperLimitComment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisCIUpperLimitComment
estimateComment
OutcomeAnalysisEstimateComment	Estimation Comment	

	MARKUP (max 250 chars)	markup ✓ (stats)	Estimation Comments			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.estimateComment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisEstimateComment
testedNonInferiority
OutcomeAnalysisTestedNonInferiority	Non-inferiority or Equivalence Test Type	

	BOOLEAN	boolean (stats)	Type of Statistical Test		Legacy Field	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.testedNonInferiority
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisTestedNonInferiority
nonInferiorityType
OutcomeAnalysisNonInferiorityType	Type of Statistical Test	

	TEXT	enum NonInferiorityType (stats)	Type of Statistical Test		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.nonInferiorityType
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisNonInferiorityType
nonInferiorityComment
OutcomeAnalysisNonInferiorityComment	Non-inferiority or Equivalence Comment	

	MARKUP (max 500 chars)	markup ✓ (stats)	Comments		Conditionally Required	resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.nonInferiorityComment
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisNonInferiorityComment
otherAnalysisDescription
OutcomeAnalysisOtherAnalysisDescription	Other Statistical Analysis	

	MARKUP (max 999 chars)	markup ✓ (stats)	Other Statistical Analysis			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.otherAnalysisDescription
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisOtherAnalysisDescription
groupDescription
OutcomeAnalysisGroupDescription	Selected Comparison Group Description	

	MARKUP (max 500 chars)	markup ✓ (stats)	Comments			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.groupDescription
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisGroupDescription
groupIds
OutcomeAnalysisGroupId		

	TEXT	text[] ✓ (stats)	Comparison Group Selection			resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.groupIds
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/OutcomeAnalysisGroupIdList/OutcomeAnalysisGroupId
numOutcomeAnalysisGroupIds ✗
NumOutcomeAnalysisGroupIds	Number of selected Arm/Groups for Analysis	

	FUNC NOccrs(OutcomeAnalysisGroupId)	short (stats)		
Number of comparison groups selected for an Analysis (internal count)
		resultsSection.outcomeMeasuresModule.outcomeMeasures.analyses.numOutcomeAnalysisGroupIds
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/OutcomeAnalysisList/OutcomeAnalysis/NumOutcomeAnalysisGroupIds
numOutcomeAnalyses ✗
NumOutcomeAnalyses	Number of Analysis	

	FUNC NOccrs(OutcomeAnalysis)	short (stats)		
Number of Analyses per Outcome Measure (internally calculated)
		resultsSection.outcomeMeasuresModule.outcomeMeasures.numOutcomeAnalyses
/Study/ResultsSection/OutcomeMeasuresModule/OutcomeMeasureList/OutcomeMeasure/NumOutcomeAnalyses
numOutcomeMeasures ✗
NumOutcomeMeasures	Number of Outcome Measures	

	FUNC NOccrs(OutcomeMeasure)	short (stats)		
Number of Outcome Measures (internally calculated)
		resultsSection.outcomeMeasuresModule.numOutcomeMeasures
/Study/ResultsSection/OutcomeMeasuresModule/NumOutcomeMeasures
adverseEventsModule
AdverseEventsModule	Adverse Events	

	STRUCT	AdverseEventsModule	Adverse Event Information			resultsSection.adverseEventsModule
/Study/ResultsSection/AdverseEventsModule
frequencyThreshold
EventsFrequencyThreshold	Frequency Threshold	

	TEXT	text ✓ (stats)	Frequency Threshold for Reporting Other (Not Including Serious) Adverse Events		Required	resultsSection.adverseEventsModule.frequencyThreshold
/Study/ResultsSection/AdverseEventsModule/EventsFrequencyThreshold
timeFrame
EventsTimeFrame	Adverse Event Time Frame	

	TEXT (max 500 chars)	text ✓ (stats)	Time Frame		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.adverseEventsModule.timeFrame
/Study/ResultsSection/AdverseEventsModule/EventsTimeFrame
description
EventsDescription	Adverse Event Reporting Description	

	MARKUP (max 500 chars)	markup ✓ (stats)	Adverse Event Reporting Description		Conditionally Required	resultsSection.adverseEventsModule.description
/Study/ResultsSection/AdverseEventsModule/EventsDescription
allCauseMortalityComment
EventsAllCauseMortalityComment	All Cause Mortality Comment	

	MARKUP	markup ✓ (stats)			Not used	resultsSection.adverseEventsModule.allCauseMortalityComment
/Study/ResultsSection/AdverseEventsModule/EventsAllCauseMortalityComment
eventGroups
EventGroup	Arm/Group	

	STRUCT	EventGroup[]	Arm/Group Information		Required	resultsSection.adverseEventsModule.eventGroups
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup
id
EventGroupId	Arm/Group ID	

	TEXT	text ✓ (stats)		
Internal group id
		resultsSection.adverseEventsModule.eventGroups.id
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupId
title
EventGroupTitle	Arm/Group Title	

	TEXT	text ✓ (stats)	Arm/Group Title		Required	resultsSection.adverseEventsModule.eventGroups.title
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupTitle
description
EventGroupDescription	Arm/Group Description	

	MARKUP (max 1500 chars)	markup ✓ (stats)	Arm/Group Description		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.adverseEventsModule.eventGroups.description
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupDescription
deathsNumAffected
EventGroupDeathsNumAffected	Total Number Affected by All-Cause Mortality	

	TEXT	integer (stats)	Total Number Affected by All-Cause Mortality		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.adverseEventsModule.eventGroups.deathsNumAffected
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupDeathsNumAffected
deathsNumAtRisk
EventGroupDeathsNumAtRisk	Total Number at Risk for All-Cause Mortality	

	TEXT	integer (stats)	Total Number at Risk for All-Cause Mortality		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.adverseEventsModule.eventGroups.deathsNumAtRisk
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupDeathsNumAtRisk
seriousNumAffected
EventGroupSeriousNumAffected	Number Affected by a Serious Adverse Event	

	TEXT	integer (stats)	Total Number Affected by Any Serious Adverse Event		Required	resultsSection.adverseEventsModule.eventGroups.seriousNumAffected
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupSeriousNumAffected
seriousNumAtRisk
EventGroupSeriousNumAtRisk	Number at Risk for a Serious Adverse Event	

	TEXT	integer (stats)	Total Number at Risk for Serious Adverse Events		Required	resultsSection.adverseEventsModule.eventGroups.seriousNumAtRisk
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupSeriousNumAtRisk
otherNumAffected
EventGroupOtherNumAffected	Number Affected by Any Other Adverse Event	

	TEXT	integer (stats)	Total Number Affected by Any Other (Not Including Serious) Adverse Events Above the Frequency Threshold		Required	resultsSection.adverseEventsModule.eventGroups.otherNumAffected
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupOtherNumAffected
otherNumAtRisk
EventGroupOtherNumAtRisk	Number at Risk for Any Other Adverse Event	

	TEXT	integer (stats)	Total Number at Risk for Other (Not Including Serious) Adverse Events		Required	resultsSection.adverseEventsModule.eventGroups.otherNumAtRisk
/Study/ResultsSection/AdverseEventsModule/EventGroupList/EventGroup/EventGroupOtherNumAtRisk
numEventGroups ✗
NumEventGroups	Number of Arm/Group	

	FUNC NOccrs(EventGroup)	short (stats)		
Number of Arm/Group for Adverse Event (internal count)
		resultsSection.adverseEventsModule.numEventGroups
/Study/ResultsSection/AdverseEventsModule/NumEventGroups
seriousEvents
SeriousEvent	Serious Adverse Event	

	STRUCT	AdverseEvent[]		
A table of all anticipated and unanticipated serious adverse events, grouped by organ system, with the number and frequency of such events by arm or comparison group of the clinical study.
		resultsSection.adverseEventsModule.seriousEvents
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent
term
SeriousEventTerm	Adverse Event Term	

	TEXT (max 100 chars)	text ✓ (stats)	Adverse Event Term		Required	resultsSection.adverseEventsModule.seriousEvents.term
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventTerm
organSystem
SeriousEventOrganSystem	Organ System	

	TEXT	text ✓ (stats)	Organ System		Required	resultsSection.adverseEventsModule.seriousEvents.organSystem
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventOrganSystem
sourceVocabulary
SeriousEventSourceVocabulary	Source Vocabulary Name for Serious Adverse Event	

	TEXT (max 20 chars)	text ✓ (stats)	Source Vocabulary Name			resultsSection.adverseEventsModule.seriousEvents.sourceVocabulary
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventSourceVocabulary
assessmentType
SeriousEventAssessmentType	Collection Approach (or Collection Approach for Table Default required)	

	TEXT	enum EventAssessment (stats)	Collection Approach		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.adverseEventsModule.seriousEvents.assessmentType
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventAssessmentType
notes
SeriousEventNotes	Serious Adverse Event Term Additional Description	

	MARKUP (max 250 chars)	markup ✓ (stats)	Adverse Event Term Additional Description			resultsSection.adverseEventsModule.seriousEvents.notes
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventNotes
stats
SeriousEventStats	Statistical information	

	STRUCT	EventStats[]		
Statistical information for each Serious Adverse Event
		resultsSection.adverseEventsModule.seriousEvents.stats
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventStatsList/SeriousEventStats
groupId
SeriousEventStatsGroupId	Group ID	

	TEXT	text ✓ (stats)		
Internal Arm/Group ID
		resultsSection.adverseEventsModule.seriousEvents.stats.groupId
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventStatsList/SeriousEventStats/SeriousEventStatsGroupId
numEvents
SeriousEventStatsNumEvents	Number of Serious Events in an Arm/Group	

	TEXT	integer (stats)	Number of Events			resultsSection.adverseEventsModule.seriousEvents.stats.numEvents
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventStatsList/SeriousEventStats/SeriousEventStatsNumEvents
numAffected
SeriousEventStatsNumAffected	Number of Participants Affected	

	TEXT	integer (stats)	Number of Participants Affected		Required	resultsSection.adverseEventsModule.seriousEvents.stats.numAffected
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventStatsList/SeriousEventStats/SeriousEventStatsNumAffected
numAtRisk
SeriousEventStatsNumAtRisk	Number of Participants at Risk	

	TEXT	integer (stats)	Number of Participants at Risk		Required	resultsSection.adverseEventsModule.seriousEvents.stats.numAtRisk
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/SeriousEventStatsList/SeriousEventStats/SeriousEventStatsNumAtRisk
numSeriousEventStatss ✗
NumSeriousEventStatss	Number of Event Groups	

	FUNC NOccrs(SeriousEventStats)	short (stats)		
Number of Event Group (Arm/Group in an Adverse Event)
	internally calculated	resultsSection.adverseEventsModule.seriousEvents.numSeriousEventStatss
/Study/ResultsSection/AdverseEventsModule/SeriousEventList/SeriousEvent/NumSeriousEventStatss
numSeriousEvents ✗
NumSeriousEvents	Number of Serious Adverse Events	

	FUNC NOccrs(SeriousEvent)	short (stats)		
Number of Serious Adverse Events
	internally calculated	resultsSection.adverseEventsModule.numSeriousEvents
/Study/ResultsSection/AdverseEventsModule/NumSeriousEvents
otherEvents
OtherEvent	Other Adverse Events	

	STRUCT	AdverseEvent[]		
Other (Not Including Serious) Adverse Events - similar to Serious AE
	Required	resultsSection.adverseEventsModule.otherEvents
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent
term
OtherEventTerm		

	TEXT	text ✓ (stats)	Adverse Event Term			resultsSection.adverseEventsModule.otherEvents.term
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventTerm
organSystem
OtherEventOrganSystem		

	TEXT	text ✓ (stats)	Organ System			resultsSection.adverseEventsModule.otherEvents.organSystem
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventOrganSystem
sourceVocabulary
OtherEventSourceVocabulary		

	TEXT	text ✓ (stats)	Source Vocabulary Name			resultsSection.adverseEventsModule.otherEvents.sourceVocabulary
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventSourceVocabulary
assessmentType
OtherEventAssessmentType		

	TEXT	enum EventAssessment (stats)	Collection Approach			resultsSection.adverseEventsModule.otherEvents.assessmentType
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventAssessmentType
notes
OtherEventNotes		

	MARKUP	markup ✓ (stats)	Adverse Event Term Additional Description			resultsSection.adverseEventsModule.otherEvents.notes
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventNotes
stats
OtherEventStats		

	STRUCT	EventStats[]				resultsSection.adverseEventsModule.otherEvents.stats
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventStatsList/OtherEventStats
groupId
OtherEventStatsGroupId		

	TEXT	text ✓ (stats)				resultsSection.adverseEventsModule.otherEvents.stats.groupId
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventStatsList/OtherEventStats/OtherEventStatsGroupId
numEvents
OtherEventStatsNumEvents		

	TEXT	integer (stats)	Number of Events			resultsSection.adverseEventsModule.otherEvents.stats.numEvents
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventStatsList/OtherEventStats/OtherEventStatsNumEvents
numAffected
OtherEventStatsNumAffected		

	TEXT	integer (stats)	Number of Participants Affected			resultsSection.adverseEventsModule.otherEvents.stats.numAffected
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventStatsList/OtherEventStats/OtherEventStatsNumAffected
numAtRisk
OtherEventStatsNumAtRisk		

	TEXT	integer (stats)	Number of Participants at Risk			resultsSection.adverseEventsModule.otherEvents.stats.numAtRisk
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/OtherEventStatsList/OtherEventStats/OtherEventStatsNumAtRisk
numOtherEventStatss ✗
NumOtherEventStatss		

	FUNC NOccrs(OtherEventStats)	short (stats)				resultsSection.adverseEventsModule.otherEvents.numOtherEventStatss
/Study/ResultsSection/AdverseEventsModule/OtherEventList/OtherEvent/NumOtherEventStatss
numOtherEvents ✗
NumOtherEvents		

	FUNC NOccrs(OtherEvent)	short (stats)				resultsSection.adverseEventsModule.numOtherEvents
/Study/ResultsSection/AdverseEventsModule/NumOtherEvents
numEvents ✗
NumEvents		

	FUNC NOccrs(SeriousEvent, OtherEvent)	short (stats)				resultsSection.adverseEventsModule.numEvents
/Study/ResultsSection/AdverseEventsModule/NumEvents
moreInfoModule
MoreInfoModule	More Information Module	

	STRUCT	MoreInfoModule				resultsSection.moreInfoModule
/Study/ResultsSection/MoreInfoModule
limitationsAndCaveats
LimitationsAndCaveats	Limitations and Caveats	

	STRUCT	LimitationsAndCaveats	Limitations and Caveats			resultsSection.moreInfoModule.limitationsAndCaveats
/Study/ResultsSection/MoreInfoModule/LimitationsAndCaveats
description
LimitationsAndCaveatsDescription	Limitations and Caveats Description	

	MARKUP (max 500 chars)	markup ✓ (stats)	Overall Limitations and Caveats			resultsSection.moreInfoModule.limitationsAndCaveats.description
/Study/ResultsSection/MoreInfoModule/LimitationsAndCaveats/LimitationsAndCaveatsDescription
certainAgreement
CertainAgreement	Certain Agreement	

	STRUCT	CertainAgreement	Certain Agreements			resultsSection.moreInfoModule.certainAgreement
/Study/ResultsSection/MoreInfoModule/CertainAgreement
piSponsorEmployee
AgreementPISponsorEmployee	Agreement PI Sponsor Employee	

	BOOLEAN	boolean (stats)	Are all PIs Employees of Sponsor?		Required	resultsSection.moreInfoModule.certainAgreement.piSponsorEmployee
/Study/ResultsSection/MoreInfoModule/CertainAgreement/AgreementPISponsorEmployee
restrictionType
AgreementRestrictionType	Agreement Restriction Type	

	TEXT	enum AgreementRestrictionType (stats)	PI Disclosure Restriction Type		If PIs are not NOT employees, and there are restrictions, the types of restriction (GT60 is greater than 60, less than or equal to 180 days; LTE is less than or equal to 60 days) - Other requires a free text explanation	resultsSection.moreInfoModule.certainAgreement.restrictionType
/Study/ResultsSection/MoreInfoModule/CertainAgreement/AgreementRestrictionType
restrictiveAgreement
AgreementRestrictiveAgreement	Agreement Restrictive Agreement	

	BOOLEAN	boolean (stats)	Results Disclosure Restriction on PI(s)?		Conditionally Required	resultsSection.moreInfoModule.certainAgreement.restrictiveAgreement
/Study/ResultsSection/MoreInfoModule/CertainAgreement/AgreementRestrictiveAgreement
otherDetails
AgreementOtherDetails	Agreement Other Details	

	MARKUP (max 500 chars)	markup ✓ (stats)	Other Disclosure Restriction Description			resultsSection.moreInfoModule.certainAgreement.otherDetails
/Study/ResultsSection/MoreInfoModule/CertainAgreement/AgreementOtherDetails
pointOfContact
PointOfContact	Point of Contact	

	STRUCT	PointOfContact	Results Point of Contact			resultsSection.moreInfoModule.pointOfContact
/Study/ResultsSection/MoreInfoModule/PointOfContact
title
PointOfContactTitle	Point of Contact Title	

	TEXT (max 1000 chars)	text ✓ (stats)	Name or Official Title		Required	resultsSection.moreInfoModule.pointOfContact.title
/Study/ResultsSection/MoreInfoModule/PointOfContact/PointOfContactTitle
organization
PointOfContactOrganization	Point of Contact Organization	

	TEXT (max 255 chars)	text (stats)	Organization Name		Required	resultsSection.moreInfoModule.pointOfContact.organization
/Study/ResultsSection/MoreInfoModule/PointOfContact/PointOfContactOrganization
email
PointOfContactEMail	Point of Contact Email	

	TEXT (max 255 chars)	text (stats)	Email		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.moreInfoModule.pointOfContact.email
/Study/ResultsSection/MoreInfoModule/PointOfContact/PointOfContactEMail
phone
PointOfContactPhone	Point of Contact Phone	

	TEXT (max 30 chars)	text (stats)	Phone		Required if Primary Completion Date is on or after January 18, 2017	resultsSection.moreInfoModule.pointOfContact.phone
/Study/ResultsSection/MoreInfoModule/PointOfContact/PointOfContactPhone
phoneExt
PointOfContactPhoneExt	Point of Contact Phone Extension	

	TEXT (max 10 chars)	text (stats)	Extension (Ext.)			resultsSection.moreInfoModule.pointOfContact.phoneExt
/Study/ResultsSection/MoreInfoModule/PointOfContact/PointOfContactPhoneExt






Annotation Section
Field Name
Piece Name	Field Title	Alt Piece Names	Classic Type	Data Type	Definition	Description	Notes	Index Field
Classic XPath
annotationSection
AnnotationSection	Annotation Section	

	STRUCT	AnnotationSection		
Internally generated Annotation section
		annotationSection
/Study/AnnotationSection
annotationModule
AnnotationModule	Annotation Module	

	STRUCT	AnnotationModule				annotationSection.annotationModule
/Study/AnnotationSection/AnnotationModule
unpostedAnnotation
UnpostedAnnotation	Unposted Annotation	

	STRUCT	UnpostedAnnotation		
Tracking information for study results submission/QA review process (Results Submitted but not yet Published)
		annotationSection.annotationModule.unpostedAnnotation
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation
unpostedResponsibleParty
UnpostedResponsibleParty	Responsible Party for Unposted Events	

	TEXT	text ✓ (stats)		
Information provider (Responsible Party)
		annotationSection.annotationModule.unpostedAnnotation.unpostedResponsibleParty
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation/UnpostedResponsibleParty
unpostedEvents
UnpostedEvent	Unposted Event	

	STRUCT	UnpostedEvent[]		
A Results Release, UnRelease or Reset event
		annotationSection.annotationModule.unpostedAnnotation.unpostedEvents
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation/UnpostedEventList/UnpostedEvent
type
UnpostedEventType	Study Results Submission Type	

	TEXT	enum UnpostedEventType (stats)		
Study Results Submission Type
		annotationSection.annotationModule.unpostedAnnotation.unpostedEvents.type
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation/UnpostedEventList/UnpostedEvent/UnpostedEventType
date
UnpostedEventDate	Study Results Submission Date	

	DATE	NormalizedDate (stats)		
Study Results Submission Date
		annotationSection.annotationModule.unpostedAnnotation.unpostedEvents.date
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation/UnpostedEventList/UnpostedEvent/UnpostedEventDate
dateUnknown
UnpostedEventDateUnknown	Unposted Event Date is Unknown	

	BOOLEAN	boolean (stats)				annotationSection.annotationModule.unpostedAnnotation.unpostedEvents.dateUnknown
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation/UnpostedEventList/UnpostedEvent/UnpostedEventDateUnknown
numUnpostedEvents ✗
NumUnpostedEvents	Number of events for the Results submission/review cycle	

	FUNC NOccrs(UnpostedEvent)	short (stats)		
Number of events for the Results submission/review cycle
		annotationSection.annotationModule.unpostedAnnotation.numUnpostedEvents
/Study/AnnotationSection/AnnotationModule/UnpostedAnnotation/NumUnpostedEvents
violationAnnotation
ViolationAnnotation	FDAAA 801 Violations	

	STRUCT	ViolationAnnotation		
FDAAA 801 Violations - entered by PRS admins
		annotationSection.annotationModule.violationAnnotation
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation
violationEvents
ViolationEvent	Violation Event	

	STRUCT	ViolationEvent[]		
PRS admin can enter one of the following types and descriptions, or other text
		annotationSection.annotationModule.violationAnnotation.violationEvents
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent
type
ViolationEventType	Violation Event Type	

	TEXT	enum ViolationEventType (stats)		
•	Violation Identified by FDA
•	Correction Confirmed by FDA
•	Penalty Imposed by FDA
		annotationSection.annotationModule.violationAnnotation.violationEvents.type
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent/ViolationEventType
description
ViolationEventDescription	Violation Event Type Description	

	TEXT	text ✓ (stats)		
•	Failure to Submit. The entry for this clinical trial was not complete at the time of submission, as required by law. This may or may not have any bearing on the accuracy of the information in the entry.
•	Submission of False Information. The entry for this clinical trial was found to be false or misleading and therefore not in compliance with the law.
•	Non-submission. The entry for this clinical trial did not contain information on the primary and secondary outcomes at the time of submission, as required by law. This may or may not have any bearing on the accuracy of the information in the entry.
•	The responsible party has corrected the violation.
•	A $XX,XXX penalty was imposed against the responsible party for the violation.
		annotationSection.annotationModule.violationAnnotation.violationEvents.description
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent/ViolationEventDescription
creationDate
ViolationEventCreationDate	Violation Event Creation Date	

	DATE	NormalizedDate (stats)		
Date the violation entered in PRS
		annotationSection.annotationModule.violationAnnotation.violationEvents.creationDate
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent/ViolationEventCreationDate
issuedDate
ViolationEventIssuedDate	Violation Event Issued Date	

	DATE	NormalizedDate (stats)		
Date the FDA issued the violation
		annotationSection.annotationModule.violationAnnotation.violationEvents.issuedDate
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent/ViolationEventIssuedDate
releaseDate
ViolationEventReleaseDate	Violation Event Released Date	

	DATE	NormalizedDate (stats)		
Date the study record was submitted
		annotationSection.annotationModule.violationAnnotation.violationEvents.releaseDate
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent/ViolationEventReleaseDate
postedDate
ViolationEventPostedDate	Violation Event Posted Date	

	DATE	NormalizedDate (stats)		
Date the violation is available on clinicaltrials.gov
		annotationSection.annotationModule.violationAnnotation.violationEvents.postedDate
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/ViolationEventList/ViolationEvent/ViolationEventPostedDate
numViolationEvents ✗
NumViolationEvents	Number of Violation Events	

	FUNC NOccrs(ViolationEvent)	short (stats)		
Number of FDAAA 801 violations - related events
		annotationSection.annotationModule.violationAnnotation.numViolationEvents
/Study/AnnotationSection/AnnotationModule/ViolationAnnotation/NumViolationEvents








Document Section
Field Name
Piece Name	Field Title	Alt Piece Names	Classic Type	Data Type	Definition	Description	Notes	Index Field
Classic XPath
documentSection
DocumentSection	Document Section	

	STRUCT	DocumentSection				documentSection
/Study/DocumentSection
largeDocumentModule
LargeDocumentModule	Document Upload Module	

	STRUCT	LargeDocumentModule	A.1 Document Upload Information			documentSection.largeDocumentModule
/Study/DocumentSection/LargeDocumentModule
noSap
LargeDocNoSAP	Document Has No Statistical Analysis Plan (SAP)	

	BOOLEAN	boolean (stats)		
Indicate that No Statistical Analysis Plan (SAP) exists for this study.
	Internal data	documentSection.largeDocumentModule.noSap
/Study/DocumentSection/LargeDocumentModule/LargeDocNoSAP
largeDocs
LargeDoc	Uploaded Document Information	

	STRUCT	LargeDoc[]		
PDF/A document by data provider
		documentSection.largeDocumentModule.largeDocs
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc
typeAbbrev
LargeDocTypeAbbrev	Document Type	

	TEXT	text ✓ (stats)	Document Type			documentSection.largeDocumentModule.largeDocs.typeAbbrev
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocTypeAbbrev
hasProtocol
LargeDocHasProtocol	Document Includes Study Protocol	

	TEXT	boolean (stats)		
Indicate if document includes Study Protocol (Yes/No)
		documentSection.largeDocumentModule.largeDocs.hasProtocol
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocHasProtocol
hasSap
LargeDocHasSAP	Document Includes Statistical Analysis Plan (SAP)	

	TEXT	boolean (stats)		
Indicate is document includes Statistical Analysis Plan (Yes/No)
		documentSection.largeDocumentModule.largeDocs.hasSap
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocHasSAP
hasIcf
LargeDocHasICF	Document Includes Informed Consent Form (ICF)	

	TEXT	boolean (stats)		
Indicate if document includes Informed Consent Form (Yes/No)
		documentSection.largeDocumentModule.largeDocs.hasIcf
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocHasICF
label
LargeDocLabel	Document Label	

	TEXT	text ✓ (stats)	Subtitle			documentSection.largeDocumentModule.largeDocs.label
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocLabel
date
LargeDocDate	Document Date	

	DATE	NormalizedDate (stats)	Document Date			documentSection.largeDocumentModule.largeDocs.date
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocDate
uploadDate
LargeDocUploadDate	Document Uploaded Date	

	DATE	DateTimeMinutes (stats)		
Date the document was uploaded to PRS
	Internally generated	documentSection.largeDocumentModule.largeDocs.uploadDate
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocUploadDate
filename
LargeDocFilename	Document File Name	

	TEXT	text (stats)		
Document file name (by data provider)
		documentSection.largeDocumentModule.largeDocs.filename
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocFilename
size
LargeDocSize	Document File Size	

	FUNC FileSize(NCTId, LargeDocFilename)	long (stats)		
Document file size
	internally calculated	documentSection.largeDocumentModule.largeDocs.size
/Study/DocumentSection/LargeDocumentModule/LargeDocList/LargeDoc/LargeDocSize
numLargeDocs ✗
NumLargeDocs	Number of Uploaded Documents	

	FUNC NOccrs(LargeDoc)	short (stats)		
Number of uploaded documents for a study record (internally calculated)
		documentSection.largeDocumentModule.numLargeDocs
/Study/DocumentSection/LargeDocumentModule/NumLargeDocs







Derived Section
Field Name
Piece Name	Field Title	Alt Piece Names	Classic Type	Data Type	Definition	Description	Notes	Index Field
Classic XPath
derivedSection
DerivedSection	Derived Section	

	STRUCT	DerivedSection		
Internally Generated
		derivedSection
/Study/DerivedSection
miscInfoModule
MiscInfoModule	Misc Information Module	

	STRUCT	MiscInfoModule				derivedSection.miscInfoModule
/Study/DerivedSection/MiscInfoModule
versionHolder
VersionHolder	Version Holder	

	DATE	NormalizedDate (stats)		
The most recent date where Ingest ran successfully
		derivedSection.miscInfoModule.versionHolder
/Study/DerivedSection/MiscInfoModule/VersionHolder
removedCountries
RemovedCountry	Removed Countries	

	TEXT	text[] (stats)		
Country for which all locations have been removed from the study
		derivedSection.miscInfoModule.removedCountries
/Study/DerivedSection/MiscInfoModule/RemovedCountryList/RemovedCountry
numRemovedCountries ✗
NumRemovedCountries	Number of Removed Countries	

	FUNC NOccrs(RemovedCountry)	short (stats)		
Number of removed countries
		derivedSection.miscInfoModule.numRemovedCountries
/Study/DerivedSection/MiscInfoModule/NumRemovedCountries
submissionTracking
SubmissionTracking	Submission Tracking	

	STRUCT	SubmissionTracking		
Results submission tracking
		derivedSection.miscInfoModule.submissionTracking
/Study/DerivedSection/MiscInfoModule/SubmissionTracking
estimatedResultsFirstSubmitDate
EstimatedResultsFirstSubmitDate	Estimated Results First Submitted Date	

	DATE	NormalizedDate (stats)		
Results First Submitted Date but not yet Posted (e.g., still under QC review). ResultsFirstSubmitDate at this point is kept empty until Results is published on the public site
		derivedSection.miscInfoModule.submissionTracking.estimatedResultsFirstSubmitDate
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/EstimatedResultsFirstSubmitDate
firstMcpInfo
FirstMCPInfo	First MCP Info	

	STRUCT	FirstMcpInfo				derivedSection.miscInfoModule.submissionTracking.firstMcpInfo
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/FirstMCPInfo
postDateStruct
FirstMCPPostDateStruct		

    ResultsFirstPostedQCCommentsDateStruct 

	STRUCT	DateStruct				derivedSection.miscInfoModule.submissionTracking.firstMcpInfo.postDateStruct
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/FirstMCPInfo/FirstMCPPostDateStruct
date
FirstMCPPostDate	First MCP Posted Date	

    RESULTS-FIRST-MCP-POSTED
    ResultsFirstPostedQCCommentsDate 

	DATE	NormalizedDate (stats)		
Date of first MCP posted date
		derivedSection.miscInfoModule.submissionTracking.firstMcpInfo.postDateStruct.date
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/FirstMCPInfo/FirstMCPPostDateStruct/FirstMCPPostDate
type
FirstMCPPostDateType	First MCP Posted Date Type	

    ResultsFirstPostedQCCommentsDateType 

	TEXT	enum DateType (stats)		
Date type for first MCP posted date
		derivedSection.miscInfoModule.submissionTracking.firstMcpInfo.postDateStruct.type
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/FirstMCPInfo/FirstMCPPostDateStruct/FirstMCPPostDateType
submissionInfos
SubmissionInfo	Study Results Submission Info	

	STRUCT	SubmissionInfo[]		
Results submission cycle information of a study
		derivedSection.miscInfoModule.submissionTracking.submissionInfos
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/SubmissionInfoList/SubmissionInfo
releaseDate
SubmissionReleaseDate	Release Date	

	DATE	NormalizedDate (stats)		
Results released by DP to NLM
		derivedSection.miscInfoModule.submissionTracking.submissionInfos.releaseDate
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/SubmissionInfoList/SubmissionInfo/SubmissionReleaseDate
unreleaseDate
SubmissionUnreleaseDate	Unrelease Date	

	DATE	NormalizedDate (stats)		
Results unrelease (canceled release) by DP
		derivedSection.miscInfoModule.submissionTracking.submissionInfos.unreleaseDate
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/SubmissionInfoList/SubmissionInfo/SubmissionUnreleaseDate
unreleaseDateUnknown
SubmissionUnreleaseDateUnknown		

	BOOLEAN	boolean (stats)				derivedSection.miscInfoModule.submissionTracking.submissionInfos.unreleaseDateUnknown
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/SubmissionInfoList/SubmissionInfo/SubmissionUnreleaseDateUnknown
resetDate
SubmissionResetDate	Reset Date	

	DATE	NormalizedDate (stats)		
NLM QC reviewer reset/unlock study back to DP
		derivedSection.miscInfoModule.submissionTracking.submissionInfos.resetDate
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/SubmissionInfoList/SubmissionInfo/SubmissionResetDate
mcpReleaseN
SubmissionMCPReleaseN	Number of MCPs	

	NUMERIC	integer (stats)		
Number of Major Comment Postings of a study
		derivedSection.miscInfoModule.submissionTracking.submissionInfos.mcpReleaseN
/Study/DerivedSection/MiscInfoModule/SubmissionTracking/SubmissionInfoList/SubmissionInfo/SubmissionMCPReleaseN
conditionBrowseModule
ConditionBrowseModule	Condition Browse Module	

	STRUCT	BrowseModule		
Support for "Search By Topic"
		derivedSection.conditionBrowseModule
/Study/DerivedSection/ConditionBrowseModule
meshes
ConditionMesh	Condition MeSH Terms	

	STRUCT	Mesh[]		
MeSH terms of Condition/Diseases field
		derivedSection.conditionBrowseModule.meshes
/Study/DerivedSection/ConditionBrowseModule/ConditionMeshList/ConditionMesh
id
ConditionMeshId	Condition MeSH ID	

	TEXT	text (stats)		
MeSH ID
		derivedSection.conditionBrowseModule.meshes.id
/Study/DerivedSection/ConditionBrowseModule/ConditionMeshList/ConditionMesh/ConditionMeshId
term
ConditionMeshTerm	Condition MeSH Term	

	TEXT	text ✓ (stats)		
MeSH Heading
	Derived from user input for "conditions"	derivedSection.conditionBrowseModule.meshes.term
/Study/DerivedSection/ConditionBrowseModule/ConditionMeshList/ConditionMesh/ConditionMeshTerm
numConditionMeshes ✗
NumConditionMeshes	Number of Condition MeSH Terms	

	FUNC NOccrs(ConditionMesh)	short (stats)		
Number of condition MeSH terms
		derivedSection.conditionBrowseModule.numConditionMeshes
/Study/DerivedSection/ConditionBrowseModule/NumConditionMeshes
ancestors
ConditionAncestor	Ancestors of Condition MeSH Terms	

	STRUCT	Mesh[]		
Ancestor (higher level and more broad) terms of Condition MeSH terms in MeSH Tree hierarchy
		derivedSection.conditionBrowseModule.ancestors
/Study/DerivedSection/ConditionBrowseModule/ConditionAncestorList/ConditionAncestor
id
ConditionAncestorId	Condition Ancestor MeSH ID	

	TEXT	text (stats)		
MeSH ID
		derivedSection.conditionBrowseModule.ancestors.id
/Study/DerivedSection/ConditionBrowseModule/ConditionAncestorList/ConditionAncestor/ConditionAncestorId
term
ConditionAncestorTerm	Condition Ancestor MeSH Term	

	TEXT	text ✓ (stats)		
MeSH Heading
		derivedSection.conditionBrowseModule.ancestors.term
/Study/DerivedSection/ConditionBrowseModule/ConditionAncestorList/ConditionAncestor/ConditionAncestorTerm
numConditionAncestors ✗
NumConditionAncestors	Number of Condition Ancestor MeSH Terms	

	FUNC NOccrs(ConditionAncestor)	short (stats)				derivedSection.conditionBrowseModule.numConditionAncestors
/Study/DerivedSection/ConditionBrowseModule/NumConditionAncestors
browseLeaves
ConditionBrowseLeaf	Condition Leaf Topics	

	STRUCT	BrowseLeaf[]		
Leaf browsing topics for Condition field
		derivedSection.conditionBrowseModule.browseLeaves
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseLeafList/ConditionBrowseLeaf
id
ConditionBrowseLeafId	Condition Leaf Topic ID	

    CONDITION-BROWSE-LEAF-ID 

	TEXT	text (stats)				derivedSection.conditionBrowseModule.browseLeaves.id
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseLeafList/ConditionBrowseLeaf/ConditionBrowseLeafId
name
ConditionBrowseLeafName	Condition Leaf Topic Name	

	TEXT	text ✓ (stats)				derivedSection.conditionBrowseModule.browseLeaves.name
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseLeafList/ConditionBrowseLeaf/ConditionBrowseLeafName
asFound
ConditionBrowseLeafAsFound	Found by Condition Term	

	TEXT	text ✓ (stats)		
Normalized Condition term used to find the topic
		derivedSection.conditionBrowseModule.browseLeaves.asFound
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseLeafList/ConditionBrowseLeaf/ConditionBrowseLeafAsFound
relevance
ConditionBrowseLeafRelevance	Relevance to Condition Leaf Topic	

	TEXT	enum BrowseLeafRelevance (stats)			LOW or HIGH	derivedSection.conditionBrowseModule.browseLeaves.relevance
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseLeafList/ConditionBrowseLeaf/ConditionBrowseLeafRelevance
numConditionBrowseLeafs ✗
NumConditionBrowseLeafs	Number of Condition Leaf Topics	

	FUNC NOccrs(ConditionBrowseLeaf)	short (stats)				derivedSection.conditionBrowseModule.numConditionBrowseLeafs
/Study/DerivedSection/ConditionBrowseModule/NumConditionBrowseLeafs
browseBranches
ConditionBrowseBranch	Condition Branch Topics	

	STRUCT	BrowseBranch[]		
Branch browsing topics for Condition field
		derivedSection.conditionBrowseModule.browseBranches
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseBranchList/ConditionBrowseBranch
abbrev
ConditionBrowseBranchAbbrev	Condition Branch Topic Short Name	

    CONDITION-BROWSE-BRANCH-ABBREV 

	TEXT	text ✓ (stats)				derivedSection.conditionBrowseModule.browseBranches.abbrev
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseBranchList/ConditionBrowseBranch/ConditionBrowseBranchAbbrev
name
ConditionBrowseBranchName	Condition Branch Topic Name	

	TEXT	text ✓ (stats)				derivedSection.conditionBrowseModule.browseBranches.name
/Study/DerivedSection/ConditionBrowseModule/ConditionBrowseBranchList/ConditionBrowseBranch/ConditionBrowseBranchName
numConditionBrowseBranches ✗
NumConditionBrowseBranches	Number of Condition Branch Topics	

	FUNC NOccrs(ConditionBrowseBranch)	short (stats)				derivedSection.conditionBrowseModule.numConditionBrowseBranches
/Study/DerivedSection/ConditionBrowseModule/NumConditionBrowseBranches
interventionBrowseModule
InterventionBrowseModule	Drug/Intervention Browse Module	

	STRUCT	BrowseModule		
Support for "Search By Topic"
		derivedSection.interventionBrowseModule
/Study/DerivedSection/InterventionBrowseModule
meshes
InterventionMesh	Intervention MeSH Terms	

	STRUCT	Mesh[]		
MeSH terms of Drug/Interventions field
		derivedSection.interventionBrowseModule.meshes
/Study/DerivedSection/InterventionBrowseModule/InterventionMeshList/InterventionMesh
id
InterventionMeshId	Intervention MeSH ID	

	TEXT	text (stats)		
MeSH ID
		derivedSection.interventionBrowseModule.meshes.id
/Study/DerivedSection/InterventionBrowseModule/InterventionMeshList/InterventionMesh/InterventionMeshId
term
InterventionMeshTerm	Intervention MeSH Term	

	TEXT	text ✓ (stats)		
MeSH Heading
		derivedSection.interventionBrowseModule.meshes.term
/Study/DerivedSection/InterventionBrowseModule/InterventionMeshList/InterventionMesh/InterventionMeshTerm
numInterventionMeshes ✗
NumInterventionMeshes	Number of Intervention MeSH Terms	

	FUNC NOccrs(InterventionMesh)	short (stats)				derivedSection.interventionBrowseModule.numInterventionMeshes
/Study/DerivedSection/InterventionBrowseModule/NumInterventionMeshes
ancestors
InterventionAncestor	Ancestors of Intervention MeSH Terms	

	STRUCT	Mesh[]		
Ancestor (higher level and more broad) terms of Intervention MeSH terms in MeSH Tree hierarchy
		derivedSection.interventionBrowseModule.ancestors
/Study/DerivedSection/InterventionBrowseModule/InterventionAncestorList/InterventionAncestor
id
InterventionAncestorId	Intervention Ancestor MeSH ID	

	TEXT	text (stats)		
MeSH ID
		derivedSection.interventionBrowseModule.ancestors.id
/Study/DerivedSection/InterventionBrowseModule/InterventionAncestorList/InterventionAncestor/InterventionAncestorId
term
InterventionAncestorTerm	Intervention Ancestor MeSH Term	

	TEXT	text ✓ (stats)		
MeSH Heading
		derivedSection.interventionBrowseModule.ancestors.term
/Study/DerivedSection/InterventionBrowseModule/InterventionAncestorList/InterventionAncestor/InterventionAncestorTerm
numInterventionAncestors ✗
NumInterventionAncestors	Number of Intervention Ancestor MeSH Terms	

	FUNC NOccrs(InterventionAncestor)	short (stats)				derivedSection.interventionBrowseModule.numInterventionAncestors
/Study/DerivedSection/InterventionBrowseModule/NumInterventionAncestors
browseLeaves
InterventionBrowseLeaf	Intervention Leaf Topics	

	STRUCT	BrowseLeaf[]		
Leaf browsing topics for Intervention field
		derivedSection.interventionBrowseModule.browseLeaves
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseLeafList/InterventionBrowseLeaf
id
InterventionBrowseLeafId	Intervention Leaf Topic ID	

    INTERVENTION-BROWSE-LEAF-ID 

	TEXT	text (stats)				derivedSection.interventionBrowseModule.browseLeaves.id
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseLeafList/InterventionBrowseLeaf/InterventionBrowseLeafId
name
InterventionBrowseLeafName	Intervention Leaf Topic Name	

	TEXT	text ✓ (stats)				derivedSection.interventionBrowseModule.browseLeaves.name
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseLeafList/InterventionBrowseLeaf/InterventionBrowseLeafName
asFound
InterventionBrowseLeafAsFound	Found by Intervention Term	

	TEXT	text ✓ (stats)		
Normalized Intervention term used to find the topic
		derivedSection.interventionBrowseModule.browseLeaves.asFound
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseLeafList/InterventionBrowseLeaf/InterventionBrowseLeafAsFound
relevance
InterventionBrowseLeafRelevance	Relevance to Intervention Leaf Topic	

	TEXT	enum BrowseLeafRelevance (stats)			LOW or HIGH	derivedSection.interventionBrowseModule.browseLeaves.relevance
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseLeafList/InterventionBrowseLeaf/InterventionBrowseLeafRelevance
numInterventionBrowseLeafs ✗
NumInterventionBrowseLeafs	Number of Intervention Leaf Topics	

	FUNC NOccrs(InterventionBrowseLeaf)	short (stats)				derivedSection.interventionBrowseModule.numInterventionBrowseLeafs
/Study/DerivedSection/InterventionBrowseModule/NumInterventionBrowseLeafs
browseBranches
InterventionBrowseBranch	Intervention Branch Topics	

	STRUCT	BrowseBranch[]		
Branch browsing topics for Intervention field
		derivedSection.interventionBrowseModule.browseBranches
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseBranchList/InterventionBrowseBranch
abbrev
InterventionBrowseBranchAbbrev	Intervention Branch Topic Short Name	

    INTERVENTION-BROWSE-BRANCH-ABBREV 

	TEXT	text ✓ (stats)				derivedSection.interventionBrowseModule.browseBranches.abbrev
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseBranchList/InterventionBrowseBranch/InterventionBrowseBranchAbbrev
name
InterventionBrowseBranchName	Intervention Branch Topic Name	

	TEXT	text ✓ (stats)				derivedSection.interventionBrowseModule.browseBranches.name
/Study/DerivedSection/InterventionBrowseModule/InterventionBrowseBranchList/InterventionBrowseBranch/InterventionBrowseBranchName
numInterventionBrowseBranches ✗
NumInterventionBrowseBranches	Number of Intervention Branch Topics	

	FUNC NOccrs(InterventionBrowseBranch)	short (stats)				derivedSection.interventionBrowseModule.numInterventionBrowseBranches
/Study/DerivedSection/InterventionBrowseModule/NumInterventionBrowseBranches







Has Results
Field Name
Piece Name	Field Title	Alt Piece Names	Classic Type	Data Type	Definition	Description	Notes	Index Field
Classic XPath
hasResults
HasResults	Has Results	

	FUNC Present(ResultsFirstSubmitDate)	boolean (stats)		
Flag that indicates if a study has posted results on public site
		hasResults
/Study/HasResults







Enumeration types
Type	Value - Source Value
Status 	

    ACTIVE_NOT_RECRUITING - Active, not recruiting
    COMPLETED - Completed
    ENROLLING_BY_INVITATION - Enrolling by invitation
    NOT_YET_RECRUITING - Not yet recruiting
    RECRUITING - Recruiting
    SUSPENDED - Suspended
    TERMINATED - Terminated
    WITHDRAWN - Withdrawn
    AVAILABLE - Available
    NO_LONGER_AVAILABLE - No longer available
    TEMPORARILY_NOT_AVAILABLE - Temporarily not available
    APPROVED_FOR_MARKETING - Approved for marketing
    WITHHELD - Withheld
    UNKNOWN - Unknown status

StudyType 	

    EXPANDED_ACCESS - Expanded Access
    INTERVENTIONAL - Interventional
    OBSERVATIONAL - Observational

Phase 	

    NA - Not Applicable
    EARLY_PHASE1 - Early Phase 1
    PHASE1 - Phase 1
    PHASE2 - Phase 2
    PHASE3 - Phase 3
    PHASE4 - Phase 4

Sex 	

    FEMALE - Female
    MALE - Male
    ALL - All

StandardAge 	

    CHILD - Child
    ADULT - Adult
    OLDER_ADULT - Older Adult

SamplingMethod 	

    PROBABILITY_SAMPLE - Probability Sample
    NON_PROBABILITY_SAMPLE - Non-Probability Sample

IpdSharing 	

    YES - Yes
    NO - No
    UNDECIDED - Undecided

IpdSharingInfoType 	

    STUDY_PROTOCOL - Study Protocol
    SAP - Statistical Analysis Plan (SAP)
    ICF - Informed Consent Form (ICF)
    CSR - Clinical Study Report (CSR)
    ANALYTIC_CODE - Analytic Code

OrgStudyIdType 	

    NIH - U.S. NIH Grant/Contract
    FDA - U.S. FDA Grant/Contract
    VA - U.S. VA Grant/Contract
    CDC - U.S. CDC Grant/Contract
    AHRQ - U.S. AHRQ Grant/Contract
    SAMHSA - U.S. SAMHSA Grant/Contract

SecondaryIdType 	

    NIH - U.S. NIH Grant/Contract
    FDA - U.S. FDA Grant/Contract
    VA - U.S. VA Grant/Contract
    CDC - U.S. CDC Grant/Contract
    AHRQ - U.S. AHRQ Grant/Contract
    SAMHSA - U.S. SAMHSA Grant/Contract
    OTHER_GRANT - Other Grant/Funding Number
    EUDRACT_NUMBER - EudraCT Number
    CTIS - EU Trial (CTIS) Number
    REGISTRY - Registry Identifier
    OTHER - Other Identifier

AgencyClass 	

    NIH - NIH
    FED - FED
    OTHER_GOV - OTHER_GOV
    INDIV - INDIV
    INDUSTRY - INDUSTRY
    NETWORK - NETWORK
    AMBIG - AMBIG
    OTHER - OTHER
    UNKNOWN - UNKNOWN

ExpandedAccessStatus 	

    AVAILABLE - Available
    NO_LONGER_AVAILABLE - No longer available
    TEMPORARILY_NOT_AVAILABLE - Temporarily not available
    APPROVED_FOR_MARKETING - Approved for marketing

DateType 	

    ACTUAL - Actual
    ESTIMATED - Estimated

ResponsiblePartyType 	

    SPONSOR - Sponsor
    PRINCIPAL_INVESTIGATOR - Principal Investigator
    SPONSOR_INVESTIGATOR - Sponsor-Investigator

DesignAllocation 	

    RANDOMIZED - Randomized
    NON_RANDOMIZED - Non-Randomized
    NA - N/A

InterventionalAssignment 	

    SINGLE_GROUP - Single Group Assignment
    PARALLEL - Parallel Assignment
    CROSSOVER - Crossover Assignment
    FACTORIAL - Factorial Assignment
    SEQUENTIAL - Sequential Assignment

PrimaryPurpose 	

    TREATMENT - Treatment
    PREVENTION - Prevention
    DIAGNOSTIC - Diagnostic
    ECT - Educational/Counseling/Training
    SUPPORTIVE_CARE - Supportive Care
    SCREENING - Screening
    HEALTH_SERVICES_RESEARCH - Health Services Research
    BASIC_SCIENCE - Basic Science
    DEVICE_FEASIBILITY - Device Feasibility
    OTHER - Other

ObservationalModel 	

    COHORT - Cohort
    CASE_CONTROL - Case-Control
    CASE_ONLY - Case-Only
    CASE_CROSSOVER - Case-Crossover
    ECOLOGIC_OR_COMMUNITY - Ecologic or Community
    FAMILY_BASED - Family-Based
    DEFINED_POPULATION - Defined Population
    NATURAL_HISTORY - Natural History
    OTHER - Other

DesignTimePerspective 	

    RETROSPECTIVE - Retrospective
    PROSPECTIVE - Prospective
    CROSS_SECTIONAL - Cross-Sectional
    OTHER - Other

BioSpecRetention 	

    NONE_RETAINED - None Retained
    SAMPLES_WITH_DNA - Samples With DNA
    SAMPLES_WITHOUT_DNA - Samples Without DNA

EnrollmentType 	

    ACTUAL - Actual
    ESTIMATED - Estimated

ArmGroupType 	

    EXPERIMENTAL - Experimental
    ACTIVE_COMPARATOR - Active Comparator
    PLACEBO_COMPARATOR - Placebo Comparator
    SHAM_COMPARATOR - Sham Comparator
    NO_INTERVENTION - No Intervention
    OTHER - Other

InterventionType 	

    BEHAVIORAL - Behavioral
    BIOLOGICAL - Biological
    COMBINATION_PRODUCT - Combination Product
    DEVICE - Device
    DIAGNOSTIC_TEST - Diagnostic Test
    DIETARY_SUPPLEMENT - Dietary Supplement
    DRUG - Drug
    GENETIC - Genetic
    PROCEDURE - Procedure
    RADIATION - Radiation
    OTHER - Other

ContactRole 	

    STUDY_CHAIR - Study Chair
    STUDY_DIRECTOR - Study Director
    PRINCIPAL_INVESTIGATOR - Principal Investigator
    SUB_INVESTIGATOR - Sub-Investigator
    CONTACT - Contact

OfficialRole 	

    STUDY_CHAIR - Study Chair
    STUDY_DIRECTOR - Study Director
    PRINCIPAL_INVESTIGATOR - Principal Investigator
    SUB_INVESTIGATOR - Sub-Investigator

RecruitmentStatus 	

    ACTIVE_NOT_RECRUITING - Active, not recruiting
    COMPLETED - Completed
    ENROLLING_BY_INVITATION - Enrolling by invitation
    NOT_YET_RECRUITING - Not yet recruiting
    RECRUITING - Recruiting
    SUSPENDED - Suspended
    TERMINATED - Terminated
    WITHDRAWN - Withdrawn
    AVAILABLE - Available

ReferenceType 	

    BACKGROUND - background
    RESULT - result
    DERIVED - derived

MeasureParam 	

    GEOMETRIC_MEAN - Geometric Mean
    GEOMETRIC_LEAST_SQUARES_MEAN - Geometric Least Squares Mean
    LEAST_SQUARES_MEAN - Least Squares Mean
    LOG_MEAN - Log Mean
    MEAN - Mean
    MEDIAN - Median
    NUMBER - Number
    COUNT_OF_PARTICIPANTS - Count of Participants
    COUNT_OF_UNITS - Count of Units

MeasureDispersionType 	

    NA - Not Applicable
    STANDARD_DEVIATION - Standard Deviation
    STANDARD_ERROR - Standard Error
    INTER_QUARTILE_RANGE - Inter-Quartile Range
    FULL_RANGE - Full Range
    CONFIDENCE_80 - 80% Confidence Interval
    CONFIDENCE_90 - 90% Confidence Interval
    CONFIDENCE_95 - 95% Confidence Interval
    CONFIDENCE_975 - 97.5% Confidence Interval
    CONFIDENCE_99 - 99% Confidence Interval
    CONFIDENCE_OTHER - Other Confidence Interval Level
    GEOMETRIC_COEFFICIENT - Geometric Coefficient of Variation

OutcomeMeasureType 	

    PRIMARY - Primary
    SECONDARY - Secondary
    OTHER_PRE_SPECIFIED - Other Pre-specified
    POST_HOC - Post-Hoc

ReportingStatus 	

    NOT_POSTED - Not Posted
    POSTED - Posted

EventAssessment 	

    NON_SYSTEMATIC_ASSESSMENT - Non-systematic Assessment
    SYSTEMATIC_ASSESSMENT - Systematic Assessment

AgreementRestrictionType 	

    LTE60 - LTE60
    GT60 - GT60
    OTHER - OTHER

BrowseLeafRelevance 	

    LOW - low
    HIGH - high

DesignMasking 	

    NONE - None (Open Label)
    SINGLE - Single
    DOUBLE - Double
    TRIPLE - Triple
    QUADRUPLE - Quadruple

WhoMasked 	

    PARTICIPANT - Participant
    CARE_PROVIDER - Care Provider
    INVESTIGATOR - Investigator
    OUTCOMES_ASSESSOR - Outcomes Assessor

AnalysisDispersionType 	

    STANDARD_DEVIATION - Standard Deviation
    STANDARD_ERROR_OF_MEAN - Standard Error of the Mean

ConfidenceIntervalNumSides 	

    ONE_SIDED - 1-Sided
    TWO_SIDED - 2-Sided

NonInferiorityType 	

    SUPERIORITY - Superiority
    NON_INFERIORITY - Non-Inferiority
    EQUIVALENCE - Equivalence
    OTHER - Other
    NON_INFERIORITY_OR_EQUIVALENCE - Non-Inferiority or Equivalence
    SUPERIORITY_OR_OTHER - Superiority or Other
    NON_INFERIORITY_OR_EQUIVALENCE_LEGACY - Non-Inferiority or Equivalence (legacy)
    SUPERIORITY_OR_OTHER_LEGACY - Superiority or Other (legacy)

UnpostedEventType 	

    RESET - Reset
    RELEASE - Release
    UNRELEASE - Unrelease

ViolationEventType 	

    VIOLATION_IDENTIFIED - Violation Identified by FDA
    CORRECTION_CONFIRMED - Correction Confirmed by FDA
    PENALTY_IMPOSED - Penalty Imposed by FDA
    ISSUES_IN_LETTER_ADDRESSED_CONFIRMED - Issues in letter addressed; confirmed by FDA.





Built-in types

              /** Date in format: `yyyy-MM-dd` */
              type NormalizedDate = string;

              /** Date in one of the formats: `yyyy`, `yyyy-MM`, or `yyyy-MM-dd` */
              type PartialDate = string;

              /** DateTime in format: `yyyy-MM-dd'T'HH:mm` */
              type DateTimeMinutes = string;

              type NormalizedTime = string;

              interface GeoPoint {
              lat: number;
              lon: number;
              }
            