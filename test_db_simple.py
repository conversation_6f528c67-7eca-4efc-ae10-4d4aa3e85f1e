#!/usr/bin/env python3
"""
Simple database connection test using only standard libraries and Docker.
This tests the database setup without requiring additional Python packages.
"""

import subprocess
import sys
import time

def test_postgres_connection():
    """Test PostgreSQL connection using Docker exec."""
    print("🔍 Testing PostgreSQL connection via Docker...")
    
    try:
        # Test connection using docker exec
        result = subprocess.run([
            'docker', 'exec', 'trails-db-1', 
            'psql', '-U', 'user', '-d', 'clinical_trials', 
            '-c', 'SELECT version();'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ PostgreSQL connection successful")
            print(f"   Database version: {result.stdout.strip().split('PostgreSQL')[1].split('on')[0].strip()}")
            return True
        else:
            print(f"❌ PostgreSQL connection failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Database connection timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def create_schema_via_docker():
    """Create database schema using Docker exec."""
    print("🔧 Creating database schema via Docker...")
    
    try:
        # Copy schema file to container
        subprocess.run([
            'docker', 'cp', 'schema.sql', 'trails-db-1:/tmp/schema.sql'
        ], check=True)
        
        # Execute schema
        result = subprocess.run([
            'docker', 'exec', 'trails-db-1',
            'psql', '-U', 'user', '-d', 'clinical_trials', '-f', '/tmp/schema.sql'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Schema created successfully")
            return True
        else:
            print(f"❌ Schema creation failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating schema: {e}")
        return False

def verify_schema_via_docker():
    """Verify schema was created correctly."""
    print("🔍 Verifying database schema...")
    
    tables_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name;
    """
    
    try:
        result = subprocess.run([
            'docker', 'exec', 'trails-db-1',
            'psql', '-U', 'user', '-d', 'clinical_trials', 
            '-c', tables_query
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            tables = [line.strip() for line in result.stdout.split('\n') 
                     if line.strip() and not line.startswith('-') and 'table_name' not in line]
            print(f"✅ Found {len(tables)} tables: {', '.join(tables)}")
            
            # Check for required tables
            required_tables = ['studies', 'fetch_log']
            missing_tables = [t for t in required_tables if t not in tables]
            if missing_tables:
                print(f"❌ Missing required tables: {missing_tables}")
                return False
            else:
                print("✅ All required tables present")
                return True
        else:
            print(f"❌ Schema verification failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying schema: {e}")
        return False

def get_container_status():
    """Get Docker container status."""
    print("🐳 Checking Docker container status...")
    
    try:
        result = subprocess.run([
            'docker', 'ps', '--filter', 'name=trails-db-1', '--format', 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
            if 'trails-db-1' in result.stdout and 'Up' in result.stdout:
                return True
            else:
                print("❌ Database container is not running")
                return False
        else:
            print(f"❌ Error checking container: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting database setup test...")
    print()
    
    # Check container status
    if not get_container_status():
        print("\n💡 Try running: docker-compose up -d")
        sys.exit(1)
    
    print()
    
    # Test connection
    if not test_postgres_connection():
        sys.exit(1)
    
    print()
    
    # Create schema
    if not create_schema_via_docker():
        sys.exit(1)
    
    print()
    
    # Verify schema
    if not verify_schema_via_docker():
        sys.exit(1)
    
    print()
    print("🎉 Database setup test completed successfully!")
    print()
    print("Next steps:")
    print("1. Install Python dependencies: pip install -r requirements.txt")
    print("2. Run the enhanced fetch script: python fetch_trials_enhanced.py")

if __name__ == "__main__":
    main()