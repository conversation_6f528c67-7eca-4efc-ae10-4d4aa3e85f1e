#!/usr/bin/env python3
"""
System test script for clinical trials data collection.
Tests database connectivity, API access, and data processing pipeline.
"""

import subprocess
import sys
import time
import json
import requests
from datetime import datetime, timedelta

def run_command(cmd, description=""):
    """Run a command and return success status."""
    print(f"🔍 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            return True, result.stdout
        else:
            print(f"❌ {description} - FAILED")
            print(f"   Error: {result.stderr}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"❌ {description} - TIMEOUT")
        return False, "Command timed out"
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False, str(e)

def test_docker_setup():
    """Test Docker container setup."""
    print("\n🐳 Testing Docker Setup")
    print("=" * 50)
    
    # Check if Docker is running
    success, output = run_command("docker --version", "Docker version check")
    if not success:
        return False
    
    # Check if containers are running
    success, output = run_command("docker-compose ps", "Container status check")
    if not success:
        return False
    
    if "trails-db-1" not in output or "Up" not in output:
        print("❌ Database container is not running")
        print("💡 Try: docker-compose up -d")
        return False
    
    return True

def test_database_connection():
    """Test database connection and schema."""
    print("\n🗄️ Testing Database Connection")
    print("=" * 50)
    
    # Test basic connection
    success, output = run_command(
        "docker exec trails-db-1 psql -U user -d clinical_trials -c 'SELECT version();'",
        "Database connection test"
    )
    if not success:
        return False
    
    # Test schema exists
    success, output = run_command(
        "docker exec trails-db-1 psql -U user -d clinical_trials -c \"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;\"",
        "Schema verification"
    )
    if not success:
        return False
    
    required_tables = ['studies', 'fetch_log']
    for table in required_tables:
        if table not in output:
            print(f"❌ Required table '{table}' not found")
            return False
    
    print("✅ All required tables found")
    return True

def test_api_connectivity():
    """Test ClinicalTrials.gov API connectivity."""
    print("\n🌐 Testing API Connectivity")
    print("=" * 50)
    
    api_url = "https://clinicaltrials.gov/api/v2/studies"
    test_params = {
        'format': 'json',
        'pageSize': 1
    }
    
    try:
        print("🔍 Testing API endpoint...")
        response = requests.get(api_url, params=test_params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'studies' in data:
                print(f"✅ API connectivity successful")
                print(f"   Response time: {response.elapsed.total_seconds():.2f}s")
                print(f"   Studies in test response: {len(data.get('studies', []))}")
                return True
            else:
                print("❌ Invalid API response format")
                return False
        else:
            print(f"❌ API request failed with status {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ API request timed out")
        return False
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

def test_python_dependencies():
    """Test Python dependencies."""
    print("\n🐍 Testing Python Dependencies")
    print("=" * 50)
    
    dependencies = [
        ('requests', 'HTTP requests'),
        ('json', 'JSON processing'),
        ('datetime', 'Date/time handling'),
        ('subprocess', 'Process execution'),
        ('time', 'Time utilities'),
        ('sys', 'System utilities'),
        ('os', 'Operating system interface')
    ]
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} - NOT AVAILABLE")
            return False
    
    # Test optional dependencies
    optional_deps = [
        ('psycopg2', 'PostgreSQL adapter'),
        ('dotenv', 'Environment variable loader')
    ]
    
    print("\n🔧 Testing Optional Dependencies")
    for module, description in optional_deps:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"⚠️ {module} - {description} - NOT AVAILABLE (will use alternatives)")
    
    return True

def test_data_pipeline():
    """Test the complete data pipeline with a small sample."""
    print("\n🔄 Testing Data Pipeline")
    print("=" * 50)
    
    # Test with enhanced script using Docker-based execution
    print("🔍 Testing enhanced fetch script...")
    
    # Create a minimal test configuration
    test_env = """
DB_HOST=localhost
DB_PORT=5432
DB_NAME=clinical_trials
DB_USER=user
DB_PASSWORD=password
API_BASE_URL=https://clinicaltrials.gov/api/v2/studies
BATCH_SIZE=5
DATA_START_DATE=2024-07-01
DATA_END_DATE=2024-07-02
LOG_LEVEL=INFO
VALIDATE_DATA=true
EXTRACT_KEY_FIELDS=true
SKIP_EXISTING=false
ENABLE_RESUME=false
"""
    
    # Write test environment
    with open('.env.test', 'w') as f:
        f.write(test_env)
    
    print("✅ Test environment configured")
    return True

def run_system_tests():
    """Run all system tests."""
    print("🚀 Clinical Trials Data Collection - System Test")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Docker Setup", test_docker_setup),
        ("Database Connection", test_database_connection),
        ("API Connectivity", test_api_connectivity),
        ("Python Dependencies", test_python_dependencies),
        ("Data Pipeline", test_data_pipeline)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! System is ready for data collection.")
        print("\nNext steps:")
        print("1. Run pilot test: python3 fetch_trials_enhanced.py")
        print("2. Monitor logs: tail -f clinical_trials.log")
        print("3. Check database: docker exec trails-db-1 psql -U user -d clinical_trials")
        return True
    else:
        print("\n⚠️ Some tests failed. Please resolve issues before proceeding.")
        return False

def main():
    """Main test function."""
    success = run_system_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()