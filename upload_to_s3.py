#!/usr/bin/env python3
"""
Upload clinical trial text documents to S3 bucket.
Handles batch uploads with progress tracking and resume capability.
"""

import os
import sys
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from botocore.credentials import RefreshableCredentials
from boto3 import Session
import logging
from pathlib import Path
from datetime import datetime, timedelta
import json
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib
import time
from typing import Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('s3_upload.log')
    ]
)
logger = logging.getLogger(__name__)

class S3Uploader:
    """Handle S3 uploads for clinical trial text documents."""
    
    def __init__(self, bucket_name: str, region: str = 'us-west-2', profile: Optional[str] = None):
        self.bucket_name = bucket_name
        self.region = region
        self.profile = profile
        self.session = None
        self.s3_client = None
        self.uploaded_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.retry_count = 0
        self.errors = []
        self.progress_file = Path('s3_upload_progress.json')
        self.uploaded_files = self.load_progress()
        self.last_client_refresh = datetime.now()
        
    def load_progress(self) -> set:
        """Load previously uploaded files from progress file."""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                data = json.load(f)
                return set(data.get('uploaded_files', []))
        return set()
    
    def save_progress(self):
        """Save upload progress to file."""
        data = {
            'uploaded_files': list(self.uploaded_files),
            'last_update': datetime.now().isoformat(),
            'stats': {
                'uploaded': self.uploaded_count,
                'skipped': self.skipped_count,
                'errors': self.error_count
            }
        }
        with open(self.progress_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def initialize_s3_client(self, force_refresh: bool = False):
        """Initialize S3 client with credentials that auto-refresh."""
        try:
            # Refresh client every 30 minutes or if forced
            if force_refresh or (datetime.now() - self.last_client_refresh) > timedelta(minutes=30):
                logger.info("Refreshing S3 client...")
                self.s3_client = None
                self.session = None
            
            if not self.s3_client:
                # Create session with profile if specified
                if self.profile:
                    self.session = Session(profile_name=self.profile)
                    logger.info(f"Using AWS profile: {self.profile}")
                else:
                    self.session = Session()
                
                # Create S3 client
                self.s3_client = self.session.client('s3', region_name=self.region)
                self.last_client_refresh = datetime.now()
                
                # Test connection
                self.s3_client.head_bucket(Bucket=self.bucket_name)
                logger.info(f"✅ Connected to S3 bucket: {self.bucket_name}")
            
            return True
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                logger.error(f"❌ Bucket '{self.bucket_name}' not found")
            elif error_code == '403':
                logger.error(f"❌ Access denied to bucket '{self.bucket_name}'")
            else:
                logger.error(f"❌ Error connecting to S3: {e}")
            return False
        except NoCredentialsError:
            logger.error("❌ AWS credentials not found. Please configure AWS credentials.")
            logger.error("Try: aws configure or set AWS_PROFILE environment variable")
            return False
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file for verification."""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def upload_file(self, file_path: Path, s3_key: str, retry_count: int = 0) -> bool:
        """Upload a single file to S3 with retry logic."""
        max_retries = 3
        retry_delay = 2  # seconds
        
        try:
            # Skip if already uploaded
            if s3_key in self.uploaded_files:
                self.skipped_count += 1
                return True
            
            # Refresh client periodically
            if self.uploaded_count % 500 == 0:
                self.initialize_s3_client()
            
            # Upload file
            with open(file_path, 'rb') as f:
                self.s3_client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=f,
                    ContentType='text/plain',
                    Metadata={
                        'source': 'clinical-trials-gov',
                        'upload-date': datetime.now().isoformat(),
                        'file-hash': self.calculate_file_hash(file_path)
                    }
                )
            
            self.uploaded_files.add(s3_key)
            self.uploaded_count += 1
            
            # Save progress periodically
            if self.uploaded_count % 100 == 0:
                self.save_progress()
                logger.info(f"Progress saved: {self.uploaded_count:,} files uploaded")
            
            return True
            
        except ClientError as e:
            # Handle credential expiration and other client errors
            if retry_count < max_retries:
                self.retry_count += 1
                if 'ExpiredToken' in str(e) or 'TokenRefresh' in str(e):
                    logger.warning(f"Token expired, refreshing credentials... (retry {retry_count + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    self.initialize_s3_client(force_refresh=True)
                    return self.upload_file(file_path, s3_key, retry_count + 1)
                else:
                    logger.warning(f"Upload failed, retrying... (retry {retry_count + 1}/{max_retries})")
                    time.sleep(retry_delay * (retry_count + 1))
                    return self.upload_file(file_path, s3_key, retry_count + 1)
            else:
                self.error_count += 1
                self.errors.append({
                    'file': str(file_path),
                    's3_key': s3_key,
                    'error': str(e)
                })
                logger.error(f"Error uploading {file_path} after {max_retries} retries: {e}")
                return False
                
        except Exception as e:
            self.error_count += 1
            self.errors.append({
                'file': str(file_path),
                's3_key': s3_key,
                'error': str(e)
            })
            logger.error(f"Error uploading {file_path}: {e}")
            return False
    
    def upload_directory(self, local_dir: Path, s3_prefix: str = 'text-documents/', 
                        max_workers: int = 10, dry_run: bool = False):
        """Upload all text files from directory to S3."""
        if not self.initialize_s3_client():
            return False
        
        # Find all text files
        text_files = list(local_dir.glob('**/*.txt'))
        # Exclude the processing report
        text_files = [f for f in text_files if f.name != 'processing_report.txt']
        
        total_files = len(text_files)
        logger.info(f"Found {total_files:,} text files to upload")
        
        if dry_run:
            logger.info("DRY RUN - No files will be uploaded")
            for i, file_path in enumerate(text_files[:10]):
                relative_path = file_path.relative_to(local_dir)
                s3_key = f"{s3_prefix}{relative_path}".replace('\\', '/')
                logger.info(f"Would upload: {file_path} -> s3://{self.bucket_name}/{s3_key}")
            if total_files > 10:
                logger.info(f"... and {total_files - 10:,} more files")
            return True
        
        # Upload files in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {}
            
            for file_path in text_files:
                relative_path = file_path.relative_to(local_dir)
                s3_key = f"{s3_prefix}{relative_path}".replace('\\', '/')
                
                future = executor.submit(self.upload_file, file_path, s3_key)
                futures[future] = (file_path, s3_key)
            
            # Process completed uploads
            completed = 0
            for future in as_completed(futures):
                completed += 1
                if completed % 1000 == 0:
                    progress_pct = (completed / total_files) * 100
                    logger.info(
                        f"Upload progress: {completed:,}/{total_files:,} ({progress_pct:.1f}%) | "
                        f"Uploaded: {self.uploaded_count:,} | Skipped: {self.skipped_count:,} | "
                        f"Errors: {self.error_count:,}"
                    )
        
        # Final save
        self.save_progress()
        return True
    
    def verify_uploads(self, local_dir: Path, s3_prefix: str = 'text-documents/'):
        """Verify uploaded files match local files."""
        if not self.initialize_s3_client():
            return False
        
        logger.info("Verifying uploads...")
        
        # List objects in S3
        paginator = self.s3_client.get_paginator('list_objects_v2')
        s3_objects = {}
        
        for page in paginator.paginate(Bucket=self.bucket_name, Prefix=s3_prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    s3_objects[obj['Key']] = obj['Size']
        
        logger.info(f"Found {len(s3_objects):,} objects in S3")
        
        # Compare with local files
        text_files = [f for f in local_dir.glob('**/*.txt') if f.name != 'processing_report.txt']
        missing = []
        
        for file_path in text_files:
            relative_path = file_path.relative_to(local_dir)
            s3_key = f"{s3_prefix}{relative_path}".replace('\\', '/')
            
            if s3_key not in s3_objects:
                missing.append(str(relative_path))
        
        if missing:
            logger.warning(f"Missing {len(missing)} files in S3:")
            for f in missing[:10]:
                logger.warning(f"  - {f}")
            if len(missing) > 10:
                logger.warning(f"  ... and {len(missing) - 10} more")
        else:
            logger.info("✅ All files successfully uploaded to S3")
        
        return len(missing) == 0
    
    def generate_report(self, output_file: str = 's3_upload_report.txt'):
        """Generate upload report."""
        with open(output_file, 'w') as f:
            f.write(f"S3 Upload Report\n")
            f.write(f"Generated: {datetime.now()}\n")
            f.write(f"Bucket: {self.bucket_name}\n")
            f.write(f"{'='*50}\n\n")
            f.write(f"Total Uploaded: {self.uploaded_count:,}\n")
            f.write(f"Skipped (already uploaded): {self.skipped_count:,}\n")
            f.write(f"Errors: {self.error_count:,}\n")
            f.write(f"Success Rate: {(self.uploaded_count / (self.uploaded_count + self.error_count) * 100):.2f}%\n")
            
            if self.errors:
                f.write(f"\nError Details (first 100):\n")
                for error in self.errors[:100]:
                    f.write(f"- {error['file']}: {error['error']}\n")
                if len(self.errors) > 100:
                    f.write(f"... and {len(self.errors) - 100} more errors\n")
        
        logger.info(f"Report saved to: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Upload clinical trial text documents to S3')
    parser.add_argument('--bucket', default='trialynx-clinical-trials-gov', 
                        help='S3 bucket name')
    parser.add_argument('--region', default='us-east-1', help='AWS region')
    parser.add_argument('--profile', help='AWS profile to use (optional)')
    parser.add_argument('--input-dir', default='text_documents', 
                        help='Local directory containing text files')
    parser.add_argument('--s3-prefix', default='text-documents/', 
                        help='S3 key prefix for uploaded files')
    parser.add_argument('--max-workers', type=int, default=10, 
                        help='Maximum parallel upload threads')
    parser.add_argument('--dry-run', action='store_true', 
                        help='Show what would be uploaded without uploading')
    parser.add_argument('--verify', action='store_true', 
                        help='Verify uploads after completion')
    parser.add_argument('--reset', action='store_true',
                        help='Reset upload progress and start fresh')
    
    args = parser.parse_args()
    
    # Initialize uploader
    uploader = S3Uploader(args.bucket, args.region, args.profile)
    
    # Reset progress if requested
    if args.reset:
        if uploader.progress_file.exists():
            uploader.progress_file.unlink()
            logger.info("Upload progress reset")
        uploader.uploaded_files = set()
    
    local_dir = Path(args.input_dir)
    if not local_dir.exists():
        logger.error(f"Input directory not found: {local_dir}")
        sys.exit(1)
    
    logger.info(f"Starting S3 upload process...")
    logger.info(f"Bucket: s3://{args.bucket}/{args.s3_prefix}")
    logger.info(f"Input directory: {local_dir}")
    
    # Upload files
    success = uploader.upload_directory(
        local_dir, 
        args.s3_prefix, 
        args.max_workers,
        args.dry_run
    )
    
    if success and not args.dry_run:
        # Generate report
        uploader.generate_report()
        
        # Verify if requested
        if args.verify:
            uploader.verify_uploads(local_dir, args.s3_prefix)
        
        logger.info(f"Upload complete! Uploaded {uploader.uploaded_count:,} files")
        if uploader.error_count > 0:
            logger.warning(f"Encountered {uploader.error_count} errors during upload")

if __name__ == "__main__":
    main()